import { ref, computed } from 'vue'
import type { ChartDatasetCell } from '@/.generated/models/ChartDatasetCell'
import dayjs from 'dayjs'
import { ChartFieldDefinition } from '@/api/models'
import { TableColumnProps } from 'ant-design-vue';

// 空值占位符常量
export const EMPTY_PLACEHOLDER = 0;

export const INDICATOR_FIELDS = [Object.assign(new ChartFieldDefinition(), {
  name: '国家',
  type: 0, // String
  unit: null,
  isEdit: false,
  isIndicator: false,
  description: '国家或地区名称',
}), Object.assign(new ChartFieldDefinition(), {
  name: '指标值',
  type: 1, // Number
  unit: null,
  isEdit: true,
  isIndicator: true,
  description: '具体的指标数值',
}), Object.assign(new ChartFieldDefinition(), {
  name: '透视值',
  type: 0, // String
  unit: null,
  isEdit: true,
  isIndicator: false,
  description: '用于透视的维度值，如年份、季度等',
})]

// 简化的指标详情数据接口
export interface IndicatorDetailData {
  country: string
  pivotValue: string
  currentValue: string
  currentVia: string
  lastUpdated: string | null
  updateType: string
  url: string
  remarks: string
  history: ChartDatasetCell[]
}

// 数据位置信息接口
export interface DataPosition {
  rowIndex: number
  fieldIndex: number
  country: string
  pivotValue: string
}

export function getViaColor(via?: string): string {
  switch (via) {
    case '官方数据': return 'green'
    case '非官方数据': return 'orange'
    case '后台计算': return 'blue'
    default: return 'default'
  }
}

export function useIndicatorData(
  records: Ref<ChartDatasetCell[][][]>,
  fieldsJson: Ref<ChartFieldDefinition[]>,
  predefinedCountries: Ref<string[]>,
  isIndicatorMode: Ref<boolean>,
) {
  // 核心字段索引计算 - 简化逻辑
  const fieldIndexes = computed(() => {
    const countryIndex = fieldsJson.value.findIndex(f => f.name === '国家' && !f.isIndicator)
    const indicatorIndex = fieldsJson.value.findIndex(f => f.isIndicator)
    const pivotIndex = fieldsJson.value.findIndex((_, index) =>
      index !== countryIndex && index !== indicatorIndex
    )

    return {
      country: countryIndex,
      indicator: indicatorIndex,
      pivot: pivotIndex
    }
  })



  // 透视表列定义
  const pivotTableColumns = computed(() => {
    if (!isIndicatorMode.value) {
      return []
    }

    const columns: TableColumnProps[] = [
      {
        title: '指标',
        dataIndex: '0',
        key: '0',
        width: 80,
        align: 'center',
      },
    ]

    // 使用预定义的国家列表
    predefinedCountries.value.forEach((country, index) => {
      columns.push({
        title: country,
        dataIndex: String(index + 1),
        key: String(index + 1),
        width: 80,
        align: 'center',
      })
    })

    return columns
  })

  // 简化的数据定位函数
  function findDataPosition(country: string, pivotValue: string): DataPosition | null {
    const indexes = fieldIndexes.value

    for (let rowIndex = 0; rowIndex < records.value.length; rowIndex++) {
      const record = records.value[rowIndex]
      if (!record) continue

      const recordCountry = getCellValue(record[indexes.country])
      const recordPivot = getCellValue(record[indexes.pivot])

      if (recordCountry === country && recordPivot === pivotValue) {
        return {
          rowIndex,
          fieldIndex: indexes.indicator,
          country,
          pivotValue
        }
      }
    }
    return null
  }

  // 透视表数据 - 简化逻辑
  const pivotTableData = computed(() => {
    if (!isIndicatorMode.value) {
      return []
    }

    const indexes = fieldIndexes.value

    // 收集所有透视值
    const pivotValues = new Set<string>()
    records.value.forEach((record) => {
      const pivotValue = getCellValue(record[indexes.pivot])
      if (pivotValue) {
        pivotValues.add(pivotValue)
      }
    })

    // 生成透视表数据
    const pivotData: any[] = []
    Array.from(pivotValues).forEach((pivotValue, index) => {
      const rowData: any = {
        index: `pivot_${index}`,
        pivotValue,
        0: pivotValue, // 第一列显示透视值
      }

      // 为所有预定义国家生成列数据
      predefinedCountries.value.forEach((country, countryIndex) => {
        const position = findDataPosition(country, pivotValue)
        if (position) {
          const record = records.value[position.rowIndex]
          if (record) {
            const cellArray = record[position.fieldIndex]
            rowData[String(countryIndex + 1)] = getCellValue(cellArray)
            // 保存位置信息用于编辑
            rowData[`_meta_${countryIndex + 1}`] = position
          }
        } else {
          rowData[String(countryIndex + 1)] = ''
        }
      })

      pivotData.push(rowData)
    })
    return pivotData
  })

  // 指标详情模态框相关
  const showIndicatorDetailModal = ref(false)
  const indicatorDetailData = ref<IndicatorDetailData>({
    country: '',
    pivotValue: '',
    currentValue: '',
    currentVia: '',
    lastUpdated: '',
    updateType: '',
    url: '',
    remarks: '',
    history: []
  })

  // 获取单元格值的辅助函数
  function getCellValue(cellArray: ChartDatasetCell[] | undefined): string {
    if (!cellArray || cellArray.length === 0) return ''
    const sortedCells = [...cellArray].sort((a, b) =>
      !b.updatedAt ? Number.MIN_VALUE : dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf()
    )
    const value = sortedCells[0]?.value ?? ''
    // 过滤掉空占位符
    return value === EMPTY_PLACEHOLDER ? '' : value
  }

  // 获取有效的历史记录（过滤掉空占位符）
  function getValidHistory(cellArray: ChartDatasetCell[]): ChartDatasetCell[] {
    return cellArray
      .filter(cell => cell.value !== EMPTY_PLACEHOLDER)
      .sort((a, b) => dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
  }

  // 统一的历史记录获取函数
  function getCellHistory(rowIndex: number, fieldIndex: number): ChartDatasetCell[] {
    const cellArray = records.value[rowIndex]?.[fieldIndex] || []
    return getValidHistory(cellArray)
  }



  // 获取当前指标值的via信息
  function getCurrentCellVia(rowIndex: number, fieldIndex: number): string {
    const history = getCellHistory(rowIndex, fieldIndex)
    return history[0]?.via || ''
  }

  // 简化的透视表单元格历史记录获取
  function getPivotCellHistory(record: any, column: any): ChartDatasetCell[] {
    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return []

    const countryName = predefinedCountries.value[columnIndex - 1]
    const pivotValue = record.pivotValue

    if (!countryName || !pivotValue) return []

    const position = findDataPosition(countryName, pivotValue)
    if (position) {
      return getCellHistory(position.rowIndex, position.fieldIndex)
    }

    return []
  }

  // 获取透视表单元格的via信息
  function getPivotCellVia(record: any, column: any): string {
    const history = getPivotCellHistory(record, column)
    return history[0]?.via || ''
  }

  // 统一的指标详情数据构建函数
  function buildIndicatorDetailData(
    country: string,
    pivotValue: string,
    history: ChartDatasetCell[],
    currentCell?: ChartDatasetCell
  ): IndicatorDetailData {
    const targetCell = currentCell || history[0]

    return {
      country,
      pivotValue,
      currentValue: targetCell?.value || '',
      currentVia: targetCell?.via || '',
      lastUpdated: dateTime(targetCell?.updatedAt),
      updateType: targetCell?.updateType || '',
      url: targetCell?.url || '',
      remarks: targetCell?.remarks || '',
      history: history
    }
  }

  // 显示普通模式指标详情
  function showIndicatorDetail(rowIndex: number, fieldIndex: number) {
    const record = records.value[rowIndex]
    if (!record || !record[fieldIndex]) return

    const history = getCellHistory(rowIndex, fieldIndex)
    if (history.length === 0) return

    const indexes = fieldIndexes.value
    const country = getCellValue(record[indexes.country]) || '未知'
    const pivotValue = getCellValue(record[indexes.pivot]) || '未知'

    indicatorDetailData.value = buildIndicatorDetailData(country, pivotValue, history)
    showIndicatorDetailModal.value = true
  }

  // 显示特定记录的指标详情
  function showSpecificIndicatorDetail(rowIndex: number, fieldIndex: number, cell: ChartDatasetCell) {
    const record = records.value[rowIndex]
    if (!record || !cell) return

    const history = getCellHistory(rowIndex, fieldIndex)
    const indexes = fieldIndexes.value
    const country = getCellValue(record[indexes.country]) || '未知'
    const pivotValue = getCellValue(record[indexes.pivot]) || '未知'

    indicatorDetailData.value = buildIndicatorDetailData(country, pivotValue, history, cell)
    showIndicatorDetailModal.value = true
  }

  // 显示透视表指标详情
  function showPivotIndicatorDetail(record: any, column: any) {
    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return

    const countryName = predefinedCountries.value[columnIndex - 1]
    if (!countryName) return

    const pivotValue = record.pivotValue
    const history = getPivotCellHistory(record, column)

    if (history.length === 0) return

    indicatorDetailData.value = buildIndicatorDetailData(countryName, pivotValue, history)
    showIndicatorDetailModal.value = true
  }

  // 显示透视表特定记录的指标详情
  function showSpecificPivotIndicatorDetail(record: any, column: any, cell: ChartDatasetCell) {
    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return

    const countryName = predefinedCountries.value[columnIndex - 1]
    if (!countryName || !cell) return

    const pivotValue = record.pivotValue
    const history = getPivotCellHistory(record, column)

    indicatorDetailData.value = buildIndicatorDetailData(countryName, pivotValue, history, cell)
    showIndicatorDetailModal.value = true
  }

  // 获取默认更新内容
  function getDefaultUpdateContent(cell: ChartDatasetCell): string {
    if (cell.updateType) {
      return cell.updateType
    }
    if (cell.value !== undefined && cell.value !== null) {
      return `更新数值为 ${cell.value}`
    }
    return '数据更新'
  }

  return {
    // 状态
    showIndicatorDetailModal,
    indicatorDetailData,
    pivotTableData,
    pivotTableColumns,

    // 核心数据访问
    fieldIndexes,
    findDataPosition,

    // 数据获取方法
    getCellValue,
    getCellHistory,
    getCurrentCellVia,
    getPivotCellHistory,
    getPivotCellVia,

    // 详情显示方法
    showIndicatorDetail,
    showSpecificIndicatorDetail,
    showPivotIndicatorDetail,
    showSpecificPivotIndicatorDetail,

    // 工具方法
    getDefaultUpdateContent,
  }
}
