# 历史记录切换功能使用说明

## 新增功能

为 `useIndicatorData` 添加了完整的历史记录切换功能，允许用户在指标详情模态框中浏览和切换不同版本的历史记录。

## 可用方法

### 1. 基础切换方法

#### `switchToHistoryVersion(cell: ChartDatasetCell)`
切换到指定的历史记录版本
```typescript
// 切换到特定的历史记录
const targetCell = indicatorDetailData.value.history[2] // 第3个历史记录
switchToHistoryVersion(targetCell)
```

#### `resetToLatestVersion()`
重置到最新版本
```typescript
// 重置到最新版本
resetToLatestVersion()
```

### 2. 导航方法

#### `switchToPreviousVersion(): boolean`
切换到上一个版本（更旧的版本）
```typescript
// 切换到上一个版本，返回是否成功
const success = switchToPreviousVersion()
if (success) {
  console.log('已切换到上一个版本')
} else {
  console.log('已经是最旧的版本')
}
```

#### `switchToNextVersion(): boolean`
切换到下一个版本（更新的版本）
```typescript
// 切换到下一个版本，返回是否成功
const success = switchToNextVersion()
if (success) {
  console.log('已切换到下一个版本')
} else {
  console.log('已经是最新的版本')
}
```

### 3. 状态检查方法

#### `getCurrentVersionIndex(): number`
获取当前显示版本在历史记录中的索引
```typescript
const currentIndex = getCurrentVersionIndex()
console.log(`当前显示第 ${currentIndex + 1} 个版本`)
```

#### `isLatestVersion(): boolean`
检查是否为最新版本
```typescript
if (isLatestVersion()) {
  console.log('当前显示的是最新版本')
}
```

#### `isOldestVersion(): boolean`
检查是否为最旧版本
```typescript
if (isOldestVersion()) {
  console.log('当前显示的是最旧版本')
}
```

## 使用场景

### 1. 在指标详情模态框中添加版本导航

```vue
<template>
  <div class="version-navigation">
    <a-button 
      :disabled="isOldestVersion()" 
      @click="switchToPreviousVersion()"
    >
      上一版本
    </a-button>
    
    <span class="version-info">
      {{ getCurrentVersionIndex() + 1 }} / {{ indicatorDetailData.history.length }}
    </span>
    
    <a-button 
      :disabled="isLatestVersion()" 
      @click="switchToNextVersion()"
    >
      下一版本
    </a-button>
    
    <a-button 
      v-if="!isLatestVersion()" 
      type="primary" 
      @click="resetToLatestVersion()"
    >
      回到最新
    </a-button>
  </div>
</template>
```

### 2. 在历史记录列表中点击切换

```vue
<template>
  <div class="history-list">
    <div 
      v-for="(cell, index) in indicatorDetailData.history" 
      :key="index"
      :class="{ 'active': getCurrentVersionIndex() === index }"
      @click="switchToHistoryVersion(cell)"
    >
      <span>{{ cell.value }}</span>
      <span>{{ dateTime(cell.updatedAt) }}</span>
    </div>
  </div>
</template>
```

### 3. 键盘快捷键支持

```typescript
// 在组件中添加键盘事件监听
onMounted(() => {
  const handleKeydown = (event: KeyboardEvent) => {
    if (!showIndicatorDetailModal.value) return
    
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        switchToPreviousVersion()
        break
      case 'ArrowRight':
        event.preventDefault()
        switchToNextVersion()
        break
      case 'Home':
        event.preventDefault()
        resetToLatestVersion()
        break
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})
```

## 数据更新

当切换版本时，以下字段会自动更新：
- `currentValue`: 当前显示的数值
- `currentVia`: 当前显示的数据来源
- `lastUpdated`: 当前显示的更新时间
- `updateType`: 当前显示的更新类型
- `url`: 当前显示的数据来源URL
- `remarks`: 当前显示的采集说明

## 注意事项

1. **历史记录顺序**: 历史记录按时间倒序排列，索引0为最新版本
2. **边界检查**: 所有导航方法都包含边界检查，不会越界
3. **状态同步**: 切换版本时会自动更新所有相关的显示字段
4. **错误处理**: 如果指定的历史记录不存在，会在控制台输出警告

## 完整示例

```typescript
// 在组件中使用
const {
  indicatorDetailData,
  switchToHistoryVersion,
  resetToLatestVersion,
  getCurrentVersionIndex,
  switchToPreviousVersion,
  switchToNextVersion,
  isLatestVersion,
  isOldestVersion,
} = useIndicatorData(records, fieldsJson, predefinedCountries, isIndicatorMode)

// 版本导航逻辑
function handleVersionNavigation(direction: 'prev' | 'next' | 'latest') {
  switch (direction) {
    case 'prev':
      if (!switchToPreviousVersion()) {
        message.info('已经是最旧的版本')
      }
      break
    case 'next':
      if (!switchToNextVersion()) {
        message.info('已经是最新的版本')
      }
      break
    case 'latest':
      resetToLatestVersion()
      message.success('已回到最新版本')
      break
  }
}
```

这些方法提供了完整的历史记录浏览功能，让用户可以方便地查看和比较不同版本的指标数据。
