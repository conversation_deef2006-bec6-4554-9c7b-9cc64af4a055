import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import {
  AggregationType,
  ApiResult,
  BaseInfo,
  BaseUserRequestLog,
  BasicInformationTypeDescription,
  Chart,
  ChartChartManagement,
  ChartChartManagementEditModel,
  ChartConfig,
  ChartDataset,
  ChartDatasetCell,
  ChartDatasetView,
  ChartFieldDefinition,
  ChartFieldType,
  ChartTemplate,
  ConfigType,
  CurrentUserPasswordChangeEditModel,
  DataAndDomain,
  DataAndHotTag,
  DataAndRegion,
  DataAndTag,
  DataDomain,
  DataHotTag,
  DataHotTagView,
  DataManageModel,
  DataManageModelHotPageModel,
  DataManageModelPageView,
  DataQueryParameterEditModel,
  DataRegion,
  DataTag,
  DataTotalByGroupView,
  DataTotalByTagView,
  DataType,
  DatasetSourceType,
  DeletedFileInfo,
  Department,
  DepartmentEditModel,
  DepartmentViewModel,
  DomainGroup,
  EfCoreResourcePermission,
  EmbedFileInfo,
  FavoriteData,
  FavoriteFolder,
  FeedbackResponse,
  FeedbackStatus,
  FeedbackType,
  FieldDataType,
  FieldMapping,
  FileAttribution,
  FileType,
  FilteprojectView,
  FilterCondition,
  FilterOperator,
  FilteresultView,
  FolderType,
  FolderWithCountDto,
  FolderWithPathDto,
  GlobalStatistics,
  GroupMemberArticleCountView,
  GroupMemberCount,
  GuidIdNameViewModel,
  IActionResult,
  ILimitedResource,
  IPagedEnumerable,
  IPermissionStoreCapacities,
  IResourceMetadata,
  IResourcePermission,
  IVersioned,
  IdentityRole,
  IdentityUser,
  IdentityUserLoginLog,
  IdentityUserRole,
  InvalidModelApiResult,
  KeyValue,
  LimitedPermissionNode,
  LimitedResourceNode,
  LoginResultLog,
  MemberArticleCount,
  Metric,
  Notes,
  NotesEditModel,
  NotesViewModel,
  PackedApiResult,
  PermissionType,
  PivotConfig,
  QueryParameterByFavorite,
  RegionGroup,
  RegisteringValidationModel,
  RequestType,
  ResourceGrant,
  ResourceMetadata,
  ResourcePermission,
  ResourceType,
  ResponseType,
  RiskWarning,
  RiskWarningDataId,
  RiskWarningGroup,
  RiskWarningGroupViewModel,
  RiskWarningPageViewModel,
  RiskWarningReadViewModel,
  RiskWarningStatsDto,
  Role,
  SystemInfo,
  TagGroup,
  TemplatePreviewRequest,
  UploadFileInfo,
  UploadFileInfoResult,
  User,
  UserCreateModel,
  UserEditModel,
  UserExpirationEditModel,
  UserFeedback,
  UserFeedbackPageView,
  UserFeedbackView,
  UserLoginLog,
  UserPasswordChangeEditModel,
  UserRegisterEditModel,
  UserRequestLog,
  UserRole,
  UserRoleViewModel,
  UserTag,
  UserTagRes,
  UserViewModel,
} from "../models";

export class apiOptions {
  static async request<TData, TResult>(
    options: AxiosRequestConfig<TData>
  ): Promise<TResult> {
    return axios.request<TData, AxiosResponse<TResult>>(options) as TResult;
  }
}

export async function requestPackedApi<TData, TResult>(
  options: AxiosRequestConfig<TData>
) {
  const data = await apiOptions.request<TData, PackedApiResult<TResult>>(
    options
  );
  if (!data.success) throw new Error(data.data as string);
  return data.data as TResult;
}

export namespace Api {
  export class BaseInfoManage {
    /**
              * GetSystemBaseTypeAsync /api/BaseInfoManage/GetSystemBaseType
              * 系统基础类型
默认不允许
              */
    static async GetSystemBaseTypeAsync(
      options?: AxiosRequestConfig
    ): Promise<BasicInformationTypeDescription[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/GetSystemBaseType`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetTypeDescriptionAsync /api/BaseInfoManage/GetTypeDescription
     * 获取参数允许的类型
     */
    static async GetTypeDescriptionAsync(
      options?: AxiosRequestConfig
    ): Promise<BasicInformationTypeDescription[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/GetTypeDescription`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * VerifyData_GetAsync /api/BaseInfoManage/VerifyData
     * 验证数据是否正确
     */
    static async VerifyData_GetAsync(
      params: {
        /**数据类型*/ type?: string;
        /**数据*/
        data?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/VerifyData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * TestData_GetAsync /api/BaseInfoManage/TestData
     * 验证数据是否正确
     */
    static async TestData_GetAsync(
      params: {
        /**数据类型*/ type?: string;
        /**数据*/
        data?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<string> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/TestData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * VerifyType_GetAsync /api/BaseInfoManage/VerifyType
     * 验证类型是否正确
     */
    static async VerifyType_GetAsync(
      params: {
        /**数据类型*/ type?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/VerifyType`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetPagedAsync /api/BaseInfoManage/GetPaged
     * 获取所有基础信息(隐藏除外)
     */
    static async GetPagedAsync(
      params: { limit?: number; offset?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<BaseInfo>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/GetPaged`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetInfoAsync /api/BaseInfoManage/GetInfo
     * 基础信息内容
     */
    static async GetInfoAsync(
      params: { key?: string },
      options?: AxiosRequestConfig
    ): Promise<BaseInfo> {
      return requestPackedApi({
        method: "GET",
        url: `/api/BaseInfoManage/GetInfo`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AddTypeDescription_PostAsync /api/BaseInfoManage/AddTypeDescription
     * 添加类型
     */
    static async AddTypeDescription_PostAsync(
      data: BasicInformationTypeDescription,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/BaseInfoManage/AddTypeDescription`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Save_PostAsync /api/BaseInfoManage/Save
     * 基础信息内容
     */
    static async Save_PostAsync(
      data: BaseInfo,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/BaseInfoManage/Save`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class ChartStatistices {
    /**
     * GetDatasetsAsync /api/ChartStatistices/GetDatasets
     * 分页获取数据集列表，可按名称模糊搜索
     */
    static async GetDatasetsAsync(
      params: { name?: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<ChartDatasetView>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetDatasets`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetDatasetAsync /api/ChartStatistices/GetDataset
     * 根据 ID 获取单个数据集详细信息
     */
    static async GetDatasetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ChartDatasetView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetDataset`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetChartsAsync /api/ChartStatistices/GetCharts
     * 分页查询图表实例，支持按标题模糊查询
     */
    static async GetChartsAsync(
      params: { title?: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<ChartChartManagement>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetCharts`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetChartAsync /api/ChartStatistices/GetChart
     * 根据ID获取单个图表实例详情，包含图表类型和数据集信息
     */
    static async GetChartAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ChartChartManagement> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetChart`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * PreviewChart_GetAsync /api/ChartStatistices/PreviewChart
     * 预览图表配置，动态生成符合 ECharts 格式的配置 JSON（动态增强版）
     */
    static async PreviewChart_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<any> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/PreviewChart`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetTemplatesAsync /api/ChartStatistices/GetTemplates
     * 分页查询图表类型模板，支持按名称模糊查询
     */
    static async GetTemplatesAsync(
      params: { name?: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<ChartTemplate>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetTemplates`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetTemplateAsync /api/ChartStatistices/GetTemplate
     * 根据 ID 获取图表类型模板详情，包含字段映射信息
     */
    static async GetTemplateAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ChartTemplate> {
      return requestPackedApi({
        method: "GET",
        url: `/api/ChartStatistices/GetTemplate`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateDataset_PostAsync /api/ChartStatistices/CreateDataset
     * 新增数据集
     */
    static async CreateDataset_PostAsync(
      data: ChartDatasetView,
      options?: AxiosRequestConfig
    ): Promise<ChartDatasetView> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/CreateDataset`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateDataset_PostAsync /api/ChartStatistices/UpdateDataset
     * 更新数据集，使用乐观锁(UpdatedAt)防止并发冲突
     */
    static async UpdateDataset_PostAsync(
      params: { id: string },
      data: ChartDatasetView,
      options?: AxiosRequestConfig
    ): Promise<ChartDatasetView> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/UpdateDataset`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteDataset_PostAsync /api/ChartStatistices/DeleteDataset
     * 删除数据集
     */
    static async DeleteDataset_PostAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/ChartStatistices/DeleteDataset`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateChart_PostAsync /api/ChartStatistices/CreateChart
     * 创建新图表实例
     */
    static async CreateChart_PostAsync(
      data: ChartChartManagementEditModel,
      options?: AxiosRequestConfig
    ): Promise<ChartChartManagement> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/CreateChart`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateChart_PostAsync /api/ChartStatistices/UpdateChart
     * 更新图表实例，建议加乐观锁（UpdatedAt）检查，防止并发覆盖
     */
    static async UpdateChart_PostAsync(
      params: { id: string },
      data: ChartChartManagement,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/ChartStatistices/UpdateChart`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteChart_PostAsync /api/ChartStatistices/DeleteChart
     * 删除图表实例
     */
    static async DeleteChart_PostAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/ChartStatistices/DeleteChart`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * TemplatePreview_PostAsync /api/ChartStatistices/TemplatePreview
     * 模板预览
     */
    static async TemplatePreview_PostAsync(
      data: TemplatePreviewRequest,
      options?: AxiosRequestConfig
    ): Promise<any> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/TemplatePreview`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * PreviewChartByData_PostAsync /api/ChartStatistices/PreviewChartByData
     *
     */
    static async PreviewChartByData_PostAsync(
      params: { datasetId: string; templateId: string },
      data: ChartChartManagementEditModel,
      options?: AxiosRequestConfig
    ): Promise<any> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/PreviewChartByData`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateTemplate_PostAsync /api/ChartStatistices/CreateTemplate
     * 新增图表类型模板
     */
    static async CreateTemplate_PostAsync(
      data: ChartTemplate,
      options?: AxiosRequestConfig
    ): Promise<ChartTemplate> {
      return requestPackedApi({
        method: "POST",
        url: `/api/ChartStatistices/CreateTemplate`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateTemplate_PostAsync /api/ChartStatistices/UpdateTemplate
     * 更新图表类型模板，包含字段映射，建议加乐观锁（UpdatedAt）检查
     */
    static async UpdateTemplate_PostAsync(
      params: { id: string },
      data: ChartTemplate,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/ChartStatistices/UpdateTemplate`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteTemplate_PostAsync /api/ChartStatistices/DeleteTemplate
     * 删除图表类型模板
     */
    static async DeleteTemplate_PostAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/ChartStatistices/DeleteTemplate`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Crypto {
    /**
     * GetShortTokenAsync /api/Crypto/GetShortToken
     *
     */
    static async GetShortTokenAsync(
      options?: AxiosRequestConfig
    ): Promise<string> {
      return apiOptions.request({
        method: "GET",
        url: `/api/Crypto/GetShortToken`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetRsaPublicKeyAsync /api/Crypto/GetRsaPublicKey
     *
     */
    static async GetRsaPublicKeyAsync(
      options?: AxiosRequestConfig
    ): Promise<string> {
      return apiOptions.request({
        method: "GET",
        url: `/api/Crypto/GetRsaPublicKey`,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class CurrentUser {
    /**
     * Me_GetAsync /api/CurrentUser/Me
     * 用户信息
     */
    static async Me_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<UserViewModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/CurrentUser/Me`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CurrentUserLoginLogList_GetAsync /api/CurrentUser/CurrentUserLoginLogList
     * 获取当前用户的登录日志
     */
    static async CurrentUserLoginLogList_GetAsync(
      params: { offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserLoginLog>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/CurrentUser/CurrentUserLoginLogList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserModel_PostAsync /api/CurrentUser/EditUserModel
     * 修改用户信息
     */
    static async EditUserModel_PostAsync(
      data: UserEditModel,
      options?: AxiosRequestConfig
    ): Promise<UserViewModel> {
      return requestPackedApi({
        method: "POST",
        url: `/api/CurrentUser/EditUserModel`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserPassword_PostAsync /api/CurrentUser/EditUserPassword
     * 修改用户密码
     */
    static async EditUserPassword_PostAsync(
      data: CurrentUserPasswordChangeEditModel,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/CurrentUser/EditUserPassword`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class DataHotTags {
    /**
     * FindOneById_GetAsync /api/DataHotTags/FindOneById
     *
     */
    static async FindOneById_GetAsync(
      params: { tagId: string },
      options?: AxiosRequestConfig
    ): Promise<DataHotTag> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/FindOneById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllTagsAsync /api/DataHotTags/GetAllTags
     * 获取所有热门标签
     */
    static async GetAllTagsAsync(
      options?: AxiosRequestConfig
    ): Promise<DataHotTagView[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/GetAllTags`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllTagsAntCountAsync /api/DataHotTags/GetAllTagsAntCount
     * 获取所有热门标签(带数量)
     */
    static async GetAllTagsAntCountAsync(
      options?: AxiosRequestConfig
    ): Promise<KeyValue[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/GetAllTagsAntCount`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateTag_GetAsync /api/DataHotTags/CreateTag
     * 创建新标签
     */
    static async CreateTag_GetAsync(
      params: { tagName?: string },
      options?: AxiosRequestConfig
    ): Promise<DataHotTag> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/CreateTag`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateTag_GetAsync /api/DataHotTags/UpdateTag
     * 更新标签名称
     */
    static async UpdateTag_GetAsync(
      params: { tagId: string; newName?: string },
      options?: AxiosRequestConfig
    ): Promise<DataHotTag> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/UpdateTag`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteTag_GetAsync /api/DataHotTags/DeleteTag
     * 删除标签
     */
    static async DeleteTag_GetAsync(
      params: { tagId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataHotTags/DeleteTag`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AddTagToArticle_GetAsync /api/DataHotTags/AddTagToArticle
     * 为文章添加标签
     */
    static async AddTagToArticle_GetAsync(
      params: { articleId: string; tagId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataHotTags/AddTagToArticle`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveTagFromArticle_GetAsync /api/DataHotTags/RemoveTagFromArticle
     * 从文章移除标签
     */
    static async RemoveTagFromArticle_GetAsync(
      params: { articleId: string; tagId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataHotTags/RemoveTagFromArticle`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveTagByName_GetAsync /api/DataHotTags/RemoveTagByName
     * 从文章移除标签(Name)
     */
    static async RemoveTagByName_GetAsync(
      params: { articleId: string; hotHag?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataHotTags/RemoveTagByName`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetHotArticlesAsync /api/DataHotTags/GetHotArticles
     * 获取所有带热门标签的文章
     */
    static async GetHotArticlesAsync(
      options?: AxiosRequestConfig
    ): Promise<DataManageModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/GetHotArticles`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetArticlesByTagAsync /api/DataHotTags/GetArticlesByTag
     * 获取指定标签下的所有文章
     */
    static async GetArticlesByTagAsync(
      params: { tagId: string },
      options?: AxiosRequestConfig
    ): Promise<DataManageModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/GetArticlesByTag`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetArticlesByTagNameAsync /api/DataHotTags/GetArticlesByTagName
     * 获取指定标签下的所有文章(Name)
     */
    static async GetArticlesByTagNameAsync(
      params: { hotHag?: string },
      options?: AxiosRequestConfig
    ): Promise<DataManageModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataHotTags/GetArticlesByTagName`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class DataManageModels {
    /**
     * FindOneById_GetAsync /api/DataManageModels/FindOneById
     * FindOneById
     */
    static async FindOneById_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<DataManageModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataManageModels/FindOneById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * MarkAsRead_GetAsync /api/DataManageModels/MarkAsRead
     * 标记文章已读
     */
    static async MarkAsRead_GetAsync(
      params: { dataId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataManageModels/MarkAsRead`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetSoureceListAsync /api/DataManageModels/GetSoureceList
     * 获取所有文章来源
     */
    static async GetSoureceListAsync(
      options?: AxiosRequestConfig
    ): Promise<string[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DataManageModels/GetSoureceList`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ImportStructuredData_GetAsync /api/DataManageModels/ImportStructuredData
     *
     */
    static async ImportStructuredData_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/DataManageModels/ImportStructuredData`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Query_PostAsync /api/DataManageModels/Query
     * 列表页查询
     */
    static async Query_PostAsync(
      params: { isRead?: boolean; offset?: number; limit?: number },
      data: DataQueryParameterEditModel,
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<DataManageModelPageView>> {
      return requestPackedApi({
        method: "POST",
        url: `/api/DataManageModels/Query`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * QueryTotal_PostAsync /api/DataManageModels/QueryTotal
     * 标签数量
     */
    static async QueryTotal_PostAsync(
      data: DataQueryParameterEditModel,
      options?: AxiosRequestConfig
    ): Promise<DataTotalByTagView> {
      return requestPackedApi({
        method: "POST",
        url: `/api/DataManageModels/QueryTotal`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ExportMultiple_PostAsync /api/DataManageModels/ExportMultiple
     * 导出
     */
    static async ExportMultiple_PostAsync(
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<Blob> {
      return request({
        method: "POST",
        url: `/api/DataManageModels/ExportMultiple`,
        data,
        responseType: "blob",
        ...(options || {}),
      });
    }
    /**
     * ImportExcel_PostAsync /api/DataManageModels/ImportExcel
     * 导入Excel文件
     */
    static async ImportExcel_PostAsync(
      data: { file: string },
      options?: AxiosRequestConfig
    ): Promise<Blob> {
      const formData = new FormData();
      formData.append("file", data.file as any);
      return request({
        method: "POST",
        url: `/api/DataManageModels/ImportExcel`,
        data: formData,
        responseType: "blob",
        ...(options || {}),
      });
    }
  }
  export class DepartmentManage {
    /**
     * GetDepartmentByIdAsync /api/DepartmentManage/GetDepartmentById
     * 根据部门 Id 获取部门（包含其联系人及直接子部门）
     */
    static async GetDepartmentByIdAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<DepartmentViewModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DepartmentManage/GetDepartmentById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetDepartmentSimpleByIdAsync /api/DepartmentManage/GetDepartmentSimpleById
     * 根据部门 Id 获取部门
     */
    static async GetDepartmentSimpleByIdAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<DepartmentViewModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DepartmentManage/GetDepartmentSimpleById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllDepartmentsAsync /api/DepartmentManage/GetAllDepartments
     * 获取所有部门（可以根据需要扩展递归加载子孙部门）
     */
    static async GetAllDepartmentsAsync(
      options?: AxiosRequestConfig
    ): Promise<DepartmentViewModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/DepartmentManage/GetAllDepartments`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateDepartment_PostAsync /api/DepartmentManage/CreateDepartment
     * 创建一个部门（可包含联系人信息及设置父部门）
     */
    static async CreateDepartment_PostAsync(
      data: DepartmentEditModel,
      options?: AxiosRequestConfig
    ): Promise<Department> {
      return requestPackedApi({
        method: "POST",
        url: `/api/DepartmentManage/CreateDepartment`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateDepartment_PostAsync /api/DepartmentManage/UpdateDepartment
     * 更新部门信息（例如修改名称、联系人、父部门等）
     */
    static async UpdateDepartment_PostAsync(
      data: DepartmentEditModel,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/DepartmentManage/UpdateDepartment`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteDepartment_PostAsync /api/DepartmentManage/DeleteDepartment
     * 删除部门（递归删除其所有子部门）
     */
    static async DeleteDepartment_PostAsync(
      params: { departmentId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/DepartmentManage/DeleteDepartment`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class FavoriteArticles {
    /**
     * CreateFolder_GetAsync /api/FavoriteArticles/CreateFolder
     * 创建目录或子收藏夹
     */
    static async CreateFolder_GetAsync(
      params: {
        name?: string;
        remark?: string;
        type?: FolderType;
        parentId?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<FavoriteFolder> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/CreateFolder`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditFolder_GetAsync /api/FavoriteArticles/EditFolder
     * 编辑
     */
    static async EditFolder_GetAsync(
      params: { name?: string; remark?: string; id: string },
      options?: AxiosRequestConfig
    ): Promise<FavoriteFolder> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/EditFolder`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * MoveFolder_GetAsync /api/FavoriteArticles/MoveFolder
     * 移动目录到新父目录
     */
    static async MoveFolder_GetAsync(
      params: { folderId: string; newParentId?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/FavoriteArticles/MoveFolder`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * MoveData_GetAsync /api/FavoriteArticles/MoveData
     * 移动文章数据到指定收藏夹
     */
    static async MoveData_GetAsync(
      params: { dataId: string; newFolderId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/FavoriteArticles/MoveData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllFoldersWithArticleCountAsync /api/FavoriteArticles/GetAllFoldersWithArticleCount
     * 查询所有目录及文章数量
     */
    static async GetAllFoldersWithArticleCountAsync(
      options?: AxiosRequestConfig
    ): Promise<FolderWithCountDto[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/GetAllFoldersWithArticleCount`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * QueryTotalForFolder_GetAsync /api/FavoriteArticles/QueryTotalForFolder
     * 收藏夹标签数量
     */
    static async QueryTotalForFolder_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<DataTotalByTagView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/QueryTotalForFolder`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteDataFromFolder_GetAsync /api/FavoriteArticles/DeleteDataFromFolder
     * 删除收藏夹里面的指定文章
     */
    static async DeleteDataFromFolder_GetAsync(
      params: { folderId: string; dataManageModelId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/FavoriteArticles/DeleteDataFromFolder`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ClearFolderData_GetAsync /api/FavoriteArticles/ClearFolderData
     * 清空收藏夹
     */
    static async ClearFolderData_GetAsync(
      params: { folderId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/FavoriteArticles/ClearFolderData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteFolder_GetAsync /api/FavoriteArticles/DeleteFolder
     * 删除收藏夹或目录（前提：无子目录、无文章）
     */
    static async DeleteFolder_GetAsync(
      params: { folderId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/FavoriteArticles/DeleteFolder`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFoldersWithPathContainingArticleAsync /api/FavoriteArticles/GetFoldersWithPathContainingArticle
     * 查找文章收藏情况
     */
    static async GetFoldersWithPathContainingArticleAsync(
      params: { dataManageModelId: string },
      options?: AxiosRequestConfig
    ): Promise<FolderWithPathDto[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/GetFoldersWithPathContainingArticle`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AddUserTagToData_GetAsync /api/FavoriteArticles/AddUserTagToData
     * 文章添加自定义标签
     */
    static async AddUserTagToData_GetAsync(
      params: {
        /**文章id*/ dataId: string;
        /**选择一个标签*/
        userTagId?: string;
        /**或者添加一个新标签*/
        userTagName?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/AddUserTagToData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveUserTagFromData_GetAsync /api/FavoriteArticles/RemoveUserTagFromData
     * 文章移除自定义标签
     */
    static async RemoveUserTagFromData_GetAsync(
      params: {
        /**数据ID*/ dataId: string;
        /**要移除的标签ID*/
        userTagId: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/RemoveUserTagFromData`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveUserTagFromDataByName_GetAsync /api/FavoriteArticles/RemoveUserTagFromDataByName
     * 文章移除自定义标签ByName
     */
    static async RemoveUserTagFromDataByName_GetAsync(
      params: { dataId: string; tagName?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/RemoveUserTagFromDataByName`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SaveNotes_GetAsync /api/FavoriteArticles/SaveNotes
     * 更新文章笔记
     */
    static async SaveNotes_GetAsync(
      params: { dataId: string; notes?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FavoriteArticles/SaveNotes`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AddData_PostAsync /api/FavoriteArticles/AddData
     * 收藏文章数据
     */
    static async AddData_PostAsync(
      params: {
        folderId: string;
        dataManageModelId: string;
        /**笔记*/
        notes?: string;
      },
      data: UserTagRes[],
      options?: AxiosRequestConfig
    ): Promise<FavoriteData> {
      return requestPackedApi({
        method: "POST",
        url: `/api/FavoriteArticles/AddData`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetArticlesInFolder_PostAsync /api/FavoriteArticles/GetArticlesInFolder
     * 收藏文章搜索
     */
    static async GetArticlesInFolder_PostAsync(
      params: {
        folderId?: string;
        /**快速检索*/
        keyword?: string;
        offset?: number;
        limit?: number;
      },
      data: QueryParameterByFavorite,
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<DataManageModelPageView>> {
      return requestPackedApi({
        method: "POST",
        url: `/api/FavoriteArticles/GetArticlesInFolder`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Feedbacks {
    /**
     * GetUserFeedbacksByUserAsync /api/Feedbacks/GetUserFeedbacksByUser
     * 反馈列表查询
     */
    static async GetUserFeedbacksByUserAsync(
      params: { status?: FeedbackStatus; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserFeedbackPageView>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Feedbacks/GetUserFeedbacksByUser`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UserGetFeedbackById_GetAsync /api/Feedbacks/UserGetFeedbackById
     * 用户查看详情
     */
    static async UserGetFeedbackById_GetAsync(
      params: { feedbackId: string },
      options?: AxiosRequestConfig
    ): Promise<UserFeedbackView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Feedbacks/UserGetFeedbackById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetResponseAsync /api/Feedbacks/GetResponse
     * 问题处理提醒
     */
    static async GetResponseAsync(
      options?: AxiosRequestConfig
    ): Promise<Record<string, string>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Feedbacks/GetResponse`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFeedbackCountAsync /api/Feedbacks/GetFeedbackCount
     * 待处理数量
     */
    static async GetFeedbackCountAsync(
      options?: AxiosRequestConfig
    ): Promise<number> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Feedbacks/GetFeedbackCount`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFeedbackByIdAsync /api/Feedbacks/GetFeedbackById
     * 管理员查看详情
     */
    static async GetFeedbackByIdAsync(
      params: { feedbackId: string },
      options?: AxiosRequestConfig
    ): Promise<UserFeedbackView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Feedbacks/GetFeedbackById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SubmitFeedback_PostAsync /api/Feedbacks/SubmitFeedback
     * 提交用户反馈
     */
    static async SubmitFeedback_PostAsync(
      params: { type?: FeedbackType; description?: string; dataId?: string },
      options?: AxiosRequestConfig
    ): Promise<UserFeedback> {
      return requestPackedApi({
        method: "POST",
        url: `/api/Feedbacks/SubmitFeedback`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserFeedbacks_PostAsync /api/Feedbacks/GetUserFeedbacks
     * 用户反馈列表查询
     */
    static async GetUserFeedbacks_PostAsync(
      params: {
        feedbackUserId?: string;
        status?: FeedbackStatus;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserFeedbackPageView>> {
      return requestPackedApi({
        method: "POST",
        url: `/api/Feedbacks/GetUserFeedbacks`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ProcessFeedback_PostAsync /api/Feedbacks/ProcessFeedback
     * 处理反馈
     */
    static async ProcessFeedback_PostAsync(
      params: {
        feedbackId: string;
        newStatus?: FeedbackStatus;
        customResponse?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/Feedbacks/ProcessFeedback`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class FileManage {
    /**
     * GetFileUrlAsync /api/FileManage/GetFileUrl
     * 文件直链获取
     */
    static async GetFileUrlAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<string> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FileManage/GetFileUrl`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFilesAsync /api/FileManage/GetFiles
     * 获取文件列表
     */
    static async GetFilesAsync(
      params: {
        type?: FileType;
        attribution?: FileAttribution;
        fileName?: string;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UploadFileInfo>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FileManage/GetFiles`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetRemoveFilesAsync /api/FileManage/GetRemoveFiles
     * 获取回收站文件列表
     */
    static async GetRemoveFilesAsync(
      params: {
        type?: FileType;
        attribution?: FileAttribution;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<DeletedFileInfo>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/FileManage/GetRemoveFiles`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UploadPortalFile_PostAsync /api/FileManage/UploadPortalFile
     * 文件上传-门户站点文章用
     */
    static async UploadPortalFile_PostAsync(
      data: { files: string[] },
      options?: AxiosRequestConfig
    ): Promise<UploadFileInfoResult[]> {
      const formData = new FormData();
      formData.append("files", data.files as any);
      return requestPackedApi({
        method: "POST",
        url: `/api/FileManage/UploadPortalFile`,
        data: formData,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UploadCertifiedFile_PostAsync /api/FileManage/UploadCertifiedFile
     * 文件上传
     */
    static async UploadCertifiedFile_PostAsync(
      data: { files: string[] },
      options?: AxiosRequestConfig
    ): Promise<UploadFileInfoResult[]> {
      const formData = new FormData();
      formData.append("files", data.files as any);
      return requestPackedApi({
        method: "POST",
        url: `/api/FileManage/UploadCertifiedFile`,
        data: formData,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveFile_PostAsync /api/FileManage/RemoveFile
     * 删除文件
     */
    static async RemoveFile_PostAsync(
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/FileManage/RemoveFile`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveCompletelyFile_PostAsync /api/FileManage/RemoveCompletelyFile
     * 彻底删除文件
     */
    static async RemoveCompletelyFile_PostAsync(
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/FileManage/RemoveCompletelyFile`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFilesByIds_PostAsync /api/FileManage/GetFilesByIds
     * 获取文件信息
     */
    static async GetFilesByIds_PostAsync(
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<UploadFileInfo[]> {
      return requestPackedApi({
        method: "POST",
        url: `/api/FileManage/GetFilesByIds`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Gdelt {
    /**
     * GetFilteprojectRolesAsync /api/Gdelt/GetFilteprojectRoles
     * 获取GDELT的过滤规则
     */
    static async GetFilteprojectRolesAsync(
      options?: AxiosRequestConfig
    ): Promise<FilteprojectView[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Gdelt/GetFilteprojectRoles`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetFilteresultAsync /api/Gdelt/GetFilteresult
     * 获取GDELT的过滤结果
     */
    static async GetFilteresultAsync(
      params: { project_id?: number; limit?: number; skip?: number },
      options?: AxiosRequestConfig
    ): Promise<FilteresultView[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Gdelt/GetFilteresult`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Limits {
    /**
     * GetStoreCapacitiesAsync /api/Limits/GetStoreCapacities
     *
     */
    static async GetStoreCapacitiesAsync(
      options?: AxiosRequestConfig
    ): Promise<IPermissionStoreCapacities> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/GetStoreCapacities`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * List_GetAsync /api/Limits/List
     *
     */
    static async List_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<ILimitedResource[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/List`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Tree_GetAsync /api/Limits/Tree
     *
     */
    static async Tree_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<LimitedResourceNode[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/Tree`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetByRolenameAsync /api/Limits/GetByRolename
     *
     */
    static async GetByRolenameAsync(
      params: { roleName?: string },
      options?: AxiosRequestConfig
    ): Promise<IResourcePermission> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/GetByRolename`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetByUserIdAsync /api/Limits/GetByUserId
     *
     */
    static async GetByUserIdAsync(
      params: { userId?: string },
      options?: AxiosRequestConfig
    ): Promise<IResourcePermission> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/GetByUserId`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Permissions_GetAsync /api/Limits/Permissions
     *
     */
    static async Permissions_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<IVersioned<LimitedPermissionNode[]>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/Permissions`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetMetadataAsync /api/Limits/GetMetadata
     *
     */
    static async GetMetadataAsync(
      params: { id?: string },
      options?: AxiosRequestConfig
    ): Promise<IResourceMetadata> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Limits/GetMetadata`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SetByRolename_PostAsync /api/Limits/SetByRolename
     *
     */
    static async SetByRolename_PostAsync(
      params: { roleName?: string },
      data: ResourcePermission,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/Limits/SetByRolename`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SetByUserId_PostAsync /api/Limits/SetByUserId
     *
     */
    static async SetByUserId_PostAsync(
      params: { userId?: string },
      data: ResourcePermission,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/Limits/SetByUserId`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SetMetadata_PostAsync /api/Limits/SetMetadata
     *
     */
    static async SetMetadata_PostAsync(
      params: { id?: string },
      data: ResourceMetadata,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/Limits/SetMetadata`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Notes {
    /**
     * Delete_GetAsync /api/Notes/Delete
     * 删除
     */
    static async Delete_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Notes/Delete`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetListAsync /api/Notes/GetList
     * 查找
     */
    static async GetListAsync(
      params: { keyWord?: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<NotesViewModel>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Notes/GetList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * FindUserNotesById_GetAsync /api/Notes/FindUserNotesById
     * FindOneById
     */
    static async FindUserNotesById_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<NotesViewModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Notes/FindUserNotesById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Create_PostAsync /api/Notes/Create
     * 创建
     */
    static async Create_PostAsync(
      data: NotesEditModel,
      options?: AxiosRequestConfig
    ): Promise<Notes> {
      return requestPackedApi({
        method: "POST",
        url: `/api/Notes/Create`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Update_PostAsync /api/Notes/Update
     * 修改笔记
     */
    static async Update_PostAsync(
      data: NotesEditModel,
      options?: AxiosRequestConfig
    ): Promise<Notes> {
      return requestPackedApi({
        method: "POST",
        url: `/api/Notes/Update`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class RiskWarning {
    /**
     * CreateGroup_GetAsync /api/RiskWarning/CreateGroup
     * 创建新分组
     */
    static async CreateGroup_GetAsync(
      params: { groupName?: string },
      options?: AxiosRequestConfig
    ): Promise<RiskWarningGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/CreateGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateGroup_GetAsync /api/RiskWarning/UpdateGroup
     * 更新分组信息
     */
    static async UpdateGroup_GetAsync(
      params: { groupId: string; newName?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/UpdateGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ReorderGroups_GetAsync /api/RiskWarning/ReorderGroups
     * 调整分组排序顺序
     */
    static async ReorderGroups_GetAsync(
      params: {
        /**要移动的分组ID*/ groupId: string;
        /**新的位置索引（从0开始）*/
        newPosition: number;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/ReorderGroups`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteGroup_GetAsync /api/RiskWarning/DeleteGroup
     * 删除分组（会同时删除组内的查询条件）
     */
    static async DeleteGroup_GetAsync(
      params: { groupId: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/DeleteGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserGroupsWithQueriesAsync /api/RiskWarning/GetUserGroupsWithQueries
     * 获取用户所有分组（包含组内查询条件）
     */
    static async GetUserGroupsWithQueriesAsync(
      options?: AxiosRequestConfig
    ): Promise<RiskWarningGroupViewModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/GetUserGroupsWithQueries`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserGroupAsync /api/RiskWarning/GetUserGroup
     * 根据id获取分组信息
     */
    static async GetUserGroupAsync(
      params: { groupId: string; includeQueries?: boolean },
      options?: AxiosRequestConfig
    ): Promise<RiskWarningGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/GetUserGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteRiskWarning_GetAsync /api/RiskWarning/DeleteRiskWarning
     * 删除
     */
    static async DeleteRiskWarning_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/DeleteRiskWarning`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ChangeGroup_GetAsync /api/RiskWarning/ChangeGroup
     * 更改所属分组
     */
    static async ChangeGroup_GetAsync(
      params: { queryId: string; newGroupId?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/ChangeGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserRiskWarningAsync /api/RiskWarning/GetUserRiskWarning
     * FindOneRiskWarningByIdAsync
     */
    static async GetUserRiskWarningAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<RiskWarning> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/GetUserRiskWarning`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetReadAsync /api/RiskWarning/GetRead
     * 信息预警
     */
    static async GetReadAsync(
      options?: AxiosRequestConfig
    ): Promise<RiskWarningReadViewModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/GetRead`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * MarkAsRead_GetAsync /api/RiskWarning/MarkAsRead
     * 标记已读
     */
    static async MarkAsRead_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/RiskWarning/MarkAsRead`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * FindDataByQuery_PostAsync /api/RiskWarning/FindDataByQuery
     * 根据预警信息查询文章列表
     */
    static async FindDataByQuery_PostAsync(
      params: { id: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<DataManageModelPageView>> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RiskWarning/FindDataByQuery`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateRiskWarning_PostAsync /api/RiskWarning/CreateRiskWarning
     * 创建新的预警信息查询条件
     */
    static async CreateRiskWarning_PostAsync(
      params: { queryTag?: string; content?: string; groupId?: string },
      data: DataQueryParameterEditModel,
      options?: AxiosRequestConfig
    ): Promise<RiskWarning> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RiskWarning/CreateRiskWarning`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateRiskWarning_PostAsync /api/RiskWarning/UpdateRiskWarning
     * 修改预警信息查询条件
     */
    static async UpdateRiskWarning_PostAsync(
      params: { id: string },
      data: DataQueryParameterEditModel,
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RiskWarning/UpdateRiskWarning`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateRiskWarningTag_PostAsync /api/RiskWarning/UpdateRiskWarningTag
     * 修改预警信息查询条件
     */
    static async UpdateRiskWarningTag_PostAsync(
      params: { id: string; queryTag?: string; content?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RiskWarning/UpdateRiskWarningTag`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class RolesManage {
    /**
     * RoleList_PostAsync /api/RolesManage/RoleList
     * 角色列表
     */
    static async RoleList_PostAsync(
      params: { roleName?: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<Role>> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RolesManage/RoleList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
              * RoleAll_PostAsync /api/RolesManage/RoleAll
              * 角色列表
完整
              */
    static async RoleAll_PostAsync(
      params: { roleName?: string },
      options?: AxiosRequestConfig
    ): Promise<Role[]> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RolesManage/RoleAll`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * SetUserRole_PostAsync /api/RolesManage/SetUserRole
     * 设置用户角色
     */
    static async SetUserRole_PostAsync(
      params: { userId: string; roleId: string; expirationTime?: Date },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/RolesManage/SetUserRole`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveUserRole_PostAsync /api/RolesManage/RemoveUserRole
     * 删除用户角色
     */
    static async RemoveUserRole_PostAsync(
      params: { userId: string; roleId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/RolesManage/RemoveUserRole`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateRole_PostAsync /api/RolesManage/CreateRole
     * 创建角色
     */
    static async CreateRole_PostAsync(
      params: { roleName?: string },
      options?: AxiosRequestConfig
    ): Promise<Role> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RolesManage/CreateRole`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Delete_PostAsync /api/RolesManage/Delete
     * 删除角色
     */
    static async Delete_PostAsync(
      params: { roleId: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/RolesManage/Delete`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ModifyRole_PostAsync /api/RolesManage/ModifyRole
     * 修改角色
     */
    static async ModifyRole_PostAsync(
      params: { roleId: string; roleName?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/RolesManage/ModifyRole`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * ModifyRoleMenu_PostAsync /api/RolesManage/ModifyRoleMenu
     * 修改角色菜单
     */
    static async ModifyRoleMenu_PostAsync(
      params: { roleId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/RolesManage/ModifyRoleMenu`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class Statistices {
    /**
     * Statistics_GetAsync /api/Statistices/Statistics
     * 获取最新的全局统计数据
     */
    static async Statistics_GetAsync(
      params: { up?: boolean },
      options?: AxiosRequestConfig
    ): Promise<GlobalStatistics> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Statistices/Statistics`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetHotDataManageAsync /api/Statistices/GetHotDataManage
     * 近期关键信息
     */
    static async GetHotDataManageAsync(
      params: {
        /**获取前pageIndex个，默认=4*/ pageIndex?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<DataManageModelHotPageModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Statistices/GetHotDataManage`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetHotTopicAsync /api/Statistices/GetHotTopic
     * 热门信息(词云)
     */
    static async GetHotTopicAsync(
      params: { pageIndex?: number },
      options?: AxiosRequestConfig
    ): Promise<Chart[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Statistices/GetHotTopic`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RiskWarningStatistics_GetAsync /api/Statistices/RiskWarningStatistics
     * 预警信息
     */
    static async RiskWarningStatistics_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<RiskWarningStatsDto> {
      return requestPackedApi({
        method: "GET",
        url: `/api/Statistices/RiskWarningStatistics`,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class SystemBaseInfo {
    /**
     * GetAsync /api/SystemBaseInfo/Get
     * 获取站点基本信息
     */
    static async GetAsync(options?: AxiosRequestConfig): Promise<SystemInfo> {
      return requestPackedApi({
        method: "GET",
        url: `/api/SystemBaseInfo/Get`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Save_PostAsync /api/SystemBaseInfo/Save
     * 保存站点基本信息
     */
    static async Save_PostAsync(
      data: SystemInfo,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/SystemBaseInfo/Save`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class TagGroupManagements {
    /**
     * CreateTagGroup_GetAsync /api/TagGroupManagements/CreateTagGroup
     * 创建标签组
     */
    static async CreateTagGroup_GetAsync(
      params: { name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<TagGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/CreateTagGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateTagGroup_GetAsync /api/TagGroupManagements/UpdateTagGroup
     * 更新标签组
     */
    static async UpdateTagGroup_GetAsync(
      params: { groupId: string; name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/UpdateTagGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteTagGroup_GetAsync /api/TagGroupManagements/DeleteTagGroup
     * 删除标签组
     */
    static async DeleteTagGroup_GetAsync(
      params: { groupId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/DeleteTagGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AssignTagsToGroup_GetAsync /api/TagGroupManagements/AssignTagsToGroup
     * 添加标签到组成员
     */
    static async AssignTagsToGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/AssignTagsToGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveTagsFromGroup_GetAsync /api/TagGroupManagements/RemoveTagsFromGroup
     * 移除标签组成员
     */
    static async RemoveTagsFromGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/RemoveTagsFromGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateDomainGroup_GetAsync /api/TagGroupManagements/CreateDomainGroup
     * 创建领域组
     */
    static async CreateDomainGroup_GetAsync(
      params: { name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<DomainGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/CreateDomainGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateDomainGroup_GetAsync /api/TagGroupManagements/UpdateDomainGroup
     * 更新领域组
     */
    static async UpdateDomainGroup_GetAsync(
      params: { groupId: string; name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/UpdateDomainGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteDomainGroup_GetAsync /api/TagGroupManagements/DeleteDomainGroup
     * 删除领域组
     */
    static async DeleteDomainGroup_GetAsync(
      params: { groupId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/DeleteDomainGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AssignDomainsToGroup_GetAsync /api/TagGroupManagements/AssignDomainsToGroup
     * 添加领域到组成员
     */
    static async AssignDomainsToGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/AssignDomainsToGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveDomainsFromGroup_GetAsync /api/TagGroupManagements/RemoveDomainsFromGroup
     * 移除领域组成员
     */
    static async RemoveDomainsFromGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/RemoveDomainsFromGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateRegionGroup_GetAsync /api/TagGroupManagements/CreateRegionGroup
     * 创建地区组
     */
    static async CreateRegionGroup_GetAsync(
      params: { name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<RegionGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/CreateRegionGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UpdateRegionGroup_GetAsync /api/TagGroupManagements/UpdateRegionGroup
     * 更新地区组
     */
    static async UpdateRegionGroup_GetAsync(
      params: { groupId: string; name?: string; description?: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/UpdateRegionGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteRegionGroup_GetAsync /api/TagGroupManagements/DeleteRegionGroup
     * 删除地区组
     */
    static async DeleteRegionGroup_GetAsync(
      params: { groupId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/DeleteRegionGroup`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * AssignRegionsToGroup_GetAsync /api/TagGroupManagements/AssignRegionsToGroup
     * 添加地区到组成员
     */
    static async AssignRegionsToGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/AssignRegionsToGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RemoveRegionsFromGroup_GetAsync /api/TagGroupManagements/RemoveRegionsFromGroup
     * 移除地区组成员
     */
    static async RemoveRegionsFromGroup_GetAsync(
      params: { groupId: string },
      data: string[],
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/TagGroupManagements/RemoveRegionsFromGroup`,
        data,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetGroupMemberArticleCountsOptimizedAsync /api/TagGroupManagements/GetGroupMemberArticleCountsOptimized
     * 分组标签数量
     */
    static async GetGroupMemberArticleCountsOptimizedAsync(
      options?: AxiosRequestConfig
    ): Promise<GroupMemberArticleCountView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetGroupMemberArticleCountsOptimized`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * QueryGroupTotals_GetAsync /api/TagGroupManagements/QueryGroupTotals
     * 获取各分组及其成员数量统计
     */
    static async QueryGroupTotals_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<DataTotalByGroupView> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/QueryGroupTotals`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllTagGroupsAsync /api/TagGroupManagements/GetAllTagGroups
     * 获取所有标签组
     */
    static async GetAllTagGroupsAsync(
      options?: AxiosRequestConfig
    ): Promise<TagGroup[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetAllTagGroups`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetNotGroupTagsAsync /api/TagGroupManagements/GetNotGroupTags
     * 获取所有未分组标签
     */
    static async GetNotGroupTagsAsync(
      options?: AxiosRequestConfig
    ): Promise<DataTag[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetNotGroupTags`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllDomainGroupsAsync /api/TagGroupManagements/GetAllDomainGroups
     * 获取所有领域组
     */
    static async GetAllDomainGroupsAsync(
      options?: AxiosRequestConfig
    ): Promise<DomainGroup[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetAllDomainGroups`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetNotDomainGroupsAsync /api/TagGroupManagements/GetNotDomainGroups
     * 获取所有未分组领域
     */
    static async GetNotDomainGroupsAsync(
      options?: AxiosRequestConfig
    ): Promise<DataDomain[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetNotDomainGroups`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetAllRegionGroupsAsync /api/TagGroupManagements/GetAllRegionGroups
     * 获取所有地区组
     */
    static async GetAllRegionGroupsAsync(
      options?: AxiosRequestConfig
    ): Promise<RegionGroup[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetAllRegionGroups`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetTagGroupByIdAsync /api/TagGroupManagements/GetTagGroupById
     *
     */
    static async GetTagGroupByIdAsync(
      params: { groupId: string; includeTags?: boolean },
      options?: AxiosRequestConfig
    ): Promise<TagGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetTagGroupById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetDomainGroupByIdAsync /api/TagGroupManagements/GetDomainGroupById
     *
     */
    static async GetDomainGroupByIdAsync(
      params: { groupId: string; includeDomains?: boolean },
      options?: AxiosRequestConfig
    ): Promise<DomainGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetDomainGroupById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetRegionGroupByIdAsync /api/TagGroupManagements/GetRegionGroupById
     *
     */
    static async GetRegionGroupByIdAsync(
      params: { groupId: string; includeRegions?: boolean },
      options?: AxiosRequestConfig
    ): Promise<RegionGroup> {
      return requestPackedApi({
        method: "GET",
        url: `/api/TagGroupManagements/GetRegionGroupById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class UserManage {
    /**
     * GetUserListAsync /api/UserManage/GetUserList
     * 返回用户列表
     */
    static async GetUserListAsync(
      params: {
        /**角色Id*/ roleId?: string;
        /**用户名*/
        userName?: string;
        /**邮箱*/
        email?: string;
        /**电话*/
        phoneNumber?: string;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserViewModel>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/GetUserList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserViewListAsync /api/UserManage/GetUserViewList
     * 返回用户列表
     */
    static async GetUserViewListAsync(
      params: {
        /**角色Id*/ roleId?: string;
        /**用户名*/
        userName?: string;
        /**邮箱*/
        email?: string;
        /**电话*/
        phoneNumber?: string;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<GuidIdNameViewModel>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/GetUserViewList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserViewListRefAsync /api/UserManage/GetUserViewListRef
     *
     */
    static async GetUserViewListRefAsync(
      params: {
        roleId?: string;
        userName?: string;
        email?: string;
        phoneNumber?: string;
      },
      options?: AxiosRequestConfig
    ): Promise<GuidIdNameViewModel[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/GetUserViewListRef`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * GetUserViewModelAsync /api/UserManage/GetUserViewModel
     * 用户信息
     */
    static async GetUserViewModelAsync(
      params: {
        /**用户ID*/ userId: string;
      },
      options?: AxiosRequestConfig
    ): Promise<UserViewModel> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/GetUserViewModel`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UserLoginLogList_GetAsync /api/UserManage/UserLoginLogList
     * 获取用户的登录日志
     */
    static async UserLoginLogList_GetAsync(
      params: { userId: string; offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserLoginLog>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/UserLoginLogList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * LoginLogList_GetAsync /api/UserManage/LoginLogList
     * 获取所有用户的登录日志
     */
    static async LoginLogList_GetAsync(
      params: { offset?: number; limit?: number },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserLoginLog>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/LoginLogList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * RequestLogList_GetAsync /api/UserManage/RequestLogList
     * 获取所有用户的请求日志
     */
    static async RequestLogList_GetAsync(
      params: {
        /**用户行为*/ des?: string;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserRequestLog>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/RequestLogList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UserRequestLogList_GetAsync /api/UserManage/UserRequestLogList
     * 获取用户的请求日志
     */
    static async UserRequestLogList_GetAsync(
      params: {
        /**用户Id*/ userId: string;
        /**用户行为*/
        des?: string;
        offset?: number;
        limit?: number;
      },
      options?: AxiosRequestConfig
    ): Promise<IPagedEnumerable<UserRequestLog>> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserManage/UserRequestLogList`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * LockUser_GetAsync /api/UserManage/LockUser
     * 禁用用户
     */
    static async LockUser_GetAsync(
      params: { userId: string },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/UserManage/LockUser`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * UnLockUser_GetAsync /api/UserManage/UnLockUser
     * 取消禁用用户
     */
    static async UnLockUser_GetAsync(
      params: {
        /**用户Id*/ userId: string;
      },
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "GET",
        url: `/api/UserManage/UnLockUser`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserModel_PostAsync /api/UserManage/EditUserModel
     * 修改用户信息
     */
    static async EditUserModel_PostAsync(
      data: UserEditModel,
      options?: AxiosRequestConfig
    ): Promise<UserViewModel> {
      return requestPackedApi({
        method: "POST",
        url: `/api/UserManage/EditUserModel`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * DeleteUser_PostAsync /api/UserManage/DeleteUser
     * 删除用户
     */
    static async DeleteUser_PostAsync(
      params: {
        /**用户Id*/ userId: string;
      },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "POST",
        url: `/api/UserManage/DeleteUser`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserPassword_PostAsync /api/UserManage/EditUserPassword
     * 修改用户的密码
     */
    static async EditUserPassword_PostAsync(
      data: UserPasswordChangeEditModel,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/UserManage/EditUserPassword`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserExpiration_PostAsync /api/UserManage/EditUserExpiration
     * 修改用户过期时间
     */
    static async EditUserExpiration_PostAsync(
      data: UserExpirationEditModel,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/UserManage/EditUserExpiration`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * CreateUser_PostAsync /api/UserManage/CreateUser
     * 创建用户
     */
    static async CreateUser_PostAsync(
      data: UserCreateModel,
      options?: AxiosRequestConfig
    ): Promise<UserViewModel> {
      return requestPackedApi({
        method: "POST",
        url: `/api/UserManage/CreateUser`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * EditUserModifyPasswordEnd_PostAsync /api/UserManage/EditUserModifyPasswordEnd
     * 修改用户密码过期时间
     */
    static async EditUserModifyPasswordEnd_PostAsync(
      data: UserExpirationEditModel,
      options?: AxiosRequestConfig
    ): Promise<ApiResult> {
      return apiOptions.request({
        method: "POST",
        url: `/api/UserManage/EditUserModifyPasswordEnd`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class UserRegistration {
    /**
     * Register_PostAsync /api/UserRegistration/Register
     * 注册提交
     */
    static async Register_PostAsync(
      data: UserRegisterEditModel,
      options?: AxiosRequestConfig
    ): Promise<RegisteringValidationModel> {
      return requestPackedApi({
        method: "POST",
        url: `/api/UserRegistration/Register`,
        data,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
  export class UserTags {
    /**
     * FindOneById_GetAsync /api/UserTags/FindOneById
     * FindOneById
     */
    static async FindOneById_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<UserTag> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserTags/FindOneById`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * FindByUser_GetAsync /api/UserTags/FindByUser
     * 获取用户所有自定义标签
     */
    static async FindByUser_GetAsync(
      options?: AxiosRequestConfig
    ): Promise<UserTag[]> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserTags/FindByUser`,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Add_GetAsync /api/UserTags/Add
     * 新建自定义标签
     */
    static async Add_GetAsync(
      params: { userTagName?: string },
      options?: AxiosRequestConfig
    ): Promise<UserTag> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserTags/Add`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Update_GetAsync /api/UserTags/Update
     * 更新
     */
    static async Update_GetAsync(
      params: { userTagId: string; userTagName?: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserTags/Update`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
    /**
     * Delete_GetAsync /api/UserTags/Delete
     * 删除标签
     */
    static async Delete_GetAsync(
      params: { id: string },
      options?: AxiosRequestConfig
    ): Promise<boolean> {
      return requestPackedApi({
        method: "GET",
        url: `/api/UserTags/Delete`,
        params,
        responseType: "json",
        ...(options || {}),
      });
    }
  }
}
/**
 * Files_GetAsync /files/{id}
 * 跳转文件
 */ export async function Files_GetAsync(
  params: { id: string },
  options?: AxiosRequestConfig
): Promise<Blob> {
  return request({
    method: "GET",
    url: `/files/${params.id}`,
    params,
    responseType: "json",
    ...(options || {}),
  });
}
