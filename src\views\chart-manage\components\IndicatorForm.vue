<template>
  <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="数值">
      <a-input-number
        v-model:value="editingIndicatorCell.value"
        style="width: 100%"
        placeholder="请输入数值"
      />
    </a-form-item>

    <a-form-item label="数据来源URL">
      <a-input
        v-model:value="editingIndicatorCell.url"
        placeholder="请输入数据来源URL"
      />
    </a-form-item>

    <a-form-item label="源文件">
      <Upload v-model:value="editingIndicatorCell.attachments" auto-list />
    </a-form-item>

    <a-form-item label="是否官方">
      <a-select v-model:value="editingIndicatorCell.via" placeholder="请选择数据来源类型">
        <a-select-option value="官方数据">官方数据</a-select-option>
        <a-select-option value="非官方数据">非官方数据</a-select-option>
        <a-select-option value="后台计算">后台计算</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="更新类型">
      <a-select v-model:value="editingIndicatorCell.updateType" placeholder="请选择更新类型">
        <a-select-option value="错误修复">错误修复</a-select-option>
        <a-select-option value="新指标">新指标</a-select-option>
        <a-select-option value="后台计算">后台计算</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="采集说明">
      <a-textarea
        v-model:value="editingIndicatorCell.remarks"
        :rows="3"
        placeholder="请输入采集说明"
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ChartDatasetCell } from '@/api/models'

const editingIndicatorCell = defineModel('value', { default: new ChartDatasetCell() })
</script>

<style scoped>

</style>
