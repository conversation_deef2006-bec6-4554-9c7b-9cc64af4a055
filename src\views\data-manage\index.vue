<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <div class="mt4 h-0 flex flex-1 gap4">
        <!-- 左侧检索区 -->
        <div class="search-box h-[calc(100vh-140px)] min-w-80 w-80 overflow-y-auto">
          <a-affix :offset-top="20">
            <ArticleFilter
              v-model:search-obj="searchObj"
              @search="onSearch"
              @reset="onSearch"
            />
          </a-affix>
        </div>
        <!-- 右侧数据表格 -->
        <div class="flex-1">
          <c-pro-table
            ref="tableRef"
            :api="api.DataManageModels.Query_PostAsync"
            :columns="columns"
            :serial-number="true"
            row-key="id"
            :post-params="searchObj"
            immediate
            bordered
          >
            <template #header>
              <a-button type="primary" @click="onImport">
                导入Excel
              </a-button>
              <a-button class="ml-2" @click="onExport">
                批量导出
              </a-button>
            </template>
            <template #bodyCell="{ record, column }">
              <span v-if="column.key === 'tags'">
                <a-tag v-for="tag in record.tags" :key="tag.id">{{ tag.name }}</a-tag>
              </span>
              <span v-if="column.key === 'hotTags'">
                <a-tag v-for="tag in record.hotTags" :key="tag">{{ tag }}</a-tag>
              </span>
              <template v-if="column.key === 'action'">
                <a class="ml-2" @click="openViewModal(record)">查看</a>
                <a-divider type="vertical" />
                <a @click="openTagEdit(record)">标签</a>
              </template>
            </template>
          </c-pro-table>
        </div>
      </div>
    </div>
    <ExcelImport
      v-model:visible="importVisible"
      title="导入Excel"
      :upload-api="handleUploadExcel"
      :temp-api="excelTemplateApi"
      :temp-title="excelTemplateTitle"
    />
    <a-modal v-model:visible="tagEditVisible" title="编辑标签" @ok="handleTagEditOk">
      <a-select
        v-model:value="currentEditArticleTags"
        :options="allTags.map(tag => ({ label: tag.name, value: tag.name }))"
        mode="multiple"
        class="w-full"
      />
    </a-modal>
    <ViewArticleModal v-model:visible="viewModalVisible" :article-id="viewArticleId" />
  </div>
</template>

<script lang="ts" setup>
import type { DataHotTagView, DataManageModelPageView } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { Api as api, DataHotTags } from '@/api'

import { DataQueryParameterEditModel } from '@/api/models'
import ExcelImport from '@/components/ExcelImport.vue'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'

// 路由声明

definePage({
  meta: {
    title: '数据管理',
    icon: 'DatabaseOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '数据管理',
        local: true,
        icon: 'DatabaseOutlined',
        order: 5,
      },
    },
  },
})

const tableRef = ref<any>(null)
const rowSelections = ref({
  selectedRowKeys: [] as string[],
  onChange: (keys: string[]) => {
    rowSelections.value.selectedRowKeys = keys
  },
})

const columns = ref<ColumnProps<any>[]>([
  { dataIndex: 'title', title: '标题', key: 'title', align: 'center' },
  { dataIndex: 'author', title: '作者', key: 'author', align: 'center' },
  { dataIndex: 'source', title: '来源', key: 'source', align: 'center' },
  { dataIndex: 'time', title: '时间', key: 'time', dateFormat: true, align: 'center' },
  // 新增标签列
  {
    dataIndex: 'hotTags',
    title: '热门标签',
    key: 'hotTags',
    align: 'center',
  },
  // 新增操作列
  {
    dataIndex: 'action',
    title: '操作',
    key: 'action',
    align: 'center',
    width: 120,
  },
])

const searchObj = ref(new DataQueryParameterEditModel())

const importVisible = ref(false)
const excelTemplateApi = '/api/DataManageModels/ExcelTemplate' // 如有模板下载接口
const excelTemplateTitle = '/数据导入模板.xlsx'

const allTags = ref<DataHotTagView[]>([])
const tagEditVisible = ref(false)
const currentArticle = ref<DataManageModelPageView | null>(null)
const currentEditArticleTags = ref<string[]>([])

const viewModalVisible = ref(false)
const viewArticleId = ref<string | undefined>(undefined)

onMounted(() => {
  fetchAllTags()
})

async function fetchAllTags() {
  allTags.value = await DataHotTags.GetAllTagsAsync()
}

async function handleUploadExcel(_?: object, formData?: FormData) {
  try {
    await api.DataManageModels.ImportExcel_PostAsync({ file: formData?.get('file') as any })
    message.success('导入成功')
    tableRef.value?.search()
    importVisible.value = false
  }
  catch (error: any) {
    message.error(`导入失败: ${error.message}`)
  }
}

function onImport() {
  importVisible.value = true
}
function onExport() {
  message.info('导出功能开发中...')
}

function openTagEdit(record: DataManageModelPageView) {
  currentArticle.value = record
  currentEditArticleTags.value = (record.hotTags as string[]).filter(Boolean)
  tagEditVisible.value = true
}

async function onTagChange() {
  const oldTags = (currentArticle.value!.hotTags ?? []).filter(Boolean)
  const added = currentEditArticleTags.value.filter(name => !oldTags.includes(name))
  const removed = oldTags.filter(name => !currentEditArticleTags.value.includes(name))
  if (!currentArticle.value?.id)
    return
  for (const tagName of added.filter(Boolean)) {
    const tag = allTags.value.find(v => v.name === tagName)
    await DataHotTags.AddTagToArticle_GetAsync({ articleId: String(currentArticle.value?.id), tagId: String(tag.id) })
  }
  for (const name of removed.filter(Boolean)) {
    await DataHotTags.RemoveTagByName_GetAsync({ articleId: String(currentArticle.value?.id), hotHag: name })
  }
}

async function handleTagEditOk() {
  tagEditVisible.value = false
  await onTagChange()
  tableRef.value?.search() // 刷新表格
}

function onSearch() {
  tableRef.value?.search()
}

function openViewModal(record: any) {
  viewArticleId.value = record.id
  viewModalVisible.value = true
}
</script>

<style scoped lang="less">
:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}
</style>
