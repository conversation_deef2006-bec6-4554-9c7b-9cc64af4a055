<template>
  <a-popover
    v-if="history && history.length"
  >
    <template #title>
      <div class="">
        <span class="">更新记录</span>
        <a-tag
          v-if="currentVia"
          :color="getViaColor(currentVia)"
          class="via-tag"
        >
          {{ currentVia }}
        </a-tag>
      </div>
    </template>

    <template #content>
      <div class="min-w80 px2 py4 space-y-2xl">
        <div
          v-for="(cell, cellIndex) in history"
          :key="cellIndex"
          class="history-item"
          :class="{ 'is-latest': cellIndex === 0 }"
        >
          <div class="flex">
            <!-- 左侧标签 -->
            <a-tag
              :color="getViaColor(cell.via || '')"
              size="small"
              class="tag-small"
            >
              {{ cell.via || '未知来源' }}
            </a-tag>
            <span class="">{{ dateTime(cell.updatedAt) }}</span>
            <a class="mlauto" @click="handleViewSource(cell)">查看溯源</a>
          </div>
          <!-- 更新内容 -->
          <div class="mt2">
            {{ cell.remarks || getDefaultUpdateContent(cell) }}
          </div>
        </div>
      </div>
    </template>

    <!-- 默认的可点击单元格 -->
    <div
      class="min-h-8 center flex cursor-pointer border border-gray-300 rounded border-dashed px-2 py-1 text-center hover:border-blue-400 hover:bg-blue-50"
      @click="handleCellClick"
    >
      <span v-if="displayValue === EMPTY_PLACEHOLDER || !displayValue" class="text-gray-400">点击编辑</span>
      <span v-else class="flex items-center gap-1">
        <!-- 三天内更新的绿点标识 -->
        <span v-if="isRecentlyUpdated" class="recent-update-dot" />
        {{ displayValue }}
      </span>
    </div>
  </a-popover>
  <!-- 默认的可点击单元格 -->
  <div
    v-else
    class="min-h-8 center flex cursor-pointer border border-gray-300 rounded border-dashed px-2 py-1 text-center hover:border-blue-400 hover:bg-blue-50"
    @click="handleCellClick"
  >
    <span v-if="displayValue === EMPTY_PLACEHOLDER || !displayValue" class="text-gray-400">点击编辑</span>
    <span v-else class="flex items-center gap-1">
      <!-- 三天内更新的绿点标识 -->
      <span v-if="isRecentlyUpdated" class="recent-update-dot" />
      {{ displayValue }}
    </span>
  </div>
</template>

<script setup lang="ts">
import type { ChartDatasetCell } from '@/.generated/models/ChartDatasetCell'
import dayjs from 'dayjs'
import { EMPTY_PLACEHOLDER, getViaColor } from '../composables/useIndicatorData'

interface Props {
  history?: ChartDatasetCell[]
  displayValue?: string | number
  onCellClick?: () => void
  onViewSource?: (cell: ChartDatasetCell) => void
}

const props = withDefaults(defineProps<Props>(), {
  history: () => [],
  displayValue: '',
  onCellClick: () => {},
  onViewSource: () => {},
})

const emit = defineEmits<{
  cellClick: []
  viewSource: [cell: ChartDatasetCell]
}>()

// 计算属性
const currentVia = computed(() => props.history[0]?.via || '')

// 检查是否为三天内更新
const isRecentlyUpdated = computed(() => {
  if (!props.history || props.history.length === 0)
    return false

  const latestUpdate = props.history[0]?.updatedAt
  if (!latestUpdate)
    return false

  const threeDaysAgo = dayjs().subtract(3, 'month')
  return dayjs(latestUpdate).isAfter(threeDaysAgo)
})

function getDefaultUpdateContent(cell: ChartDatasetCell): string {
  if (cell.updateType) {
    return cell.updateType
  }
  if (cell.value !== undefined && cell.value !== null) {
    return `更新数值为 ${cell.value}`
  }
  return '数据更新'
}

function handleCellClick() {
  emit('cellClick')
  props.onCellClick?.()
}

function handleViewSource(cell: ChartDatasetCell) {
  emit('viewSource', cell)
  props.onViewSource?.(cell)
}
</script>

<style scoped lang="less">
.history-item {
  &.is-latest {
    background-color: #f6ffed;
    padding: 8px;
    margin: -8px;
    border-radius: 4px;
    border-left: 3px solid #52c41a;
  }
}

// 三天内更新的绿点样式
.recent-update-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #52c41a;
  border-radius: 50%;
  flex-shrink: 0;

  // 添加轻微的发光效果
  box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);

  // 可选：添加脉冲动画效果
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);
  }
  50% {
    box-shadow: 0 0 8px rgba(82, 196, 26, 0.8);
  }
  100% {
    box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);
  }
}
</style>
