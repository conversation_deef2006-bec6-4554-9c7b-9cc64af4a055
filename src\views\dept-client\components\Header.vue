<template>
  <DefineTemplate v-slot="{ mode }">
    <!-- 桌面端导航 -->
    <div v-if="mode === 'horizontal'" class="custom-nav-menu" @mouseenter="showMegaMenu" @mouseleave="hideMegaMenu">
      <template v-for="(item, index) in menuConfig" :key="index">
        <div
          v-if="!item.children"
          class="nav-item"
          :class="{ active: currentPath === item.path }"
          @click="navigateTo(item.path!)"
        >
          {{ item.name }}
        </div>
        <div
          v-else
          class="nav-item has-submenu"
          :class="{ active: isMenuActive(item) }"
        >
          {{ item.name }}
        </div>
      </template>
    </div>

    <!-- 移动端导航 -->
    <div v-else class="mobile-nav-menu">
      <template v-for="(item, index) in menuConfig" :key="index">
        <div
          v-if="!item.children"
          class="nav-item"
          :class="{ active: currentPath === item.path }"
          @click="navigateTo(item.path!)"
        >
          {{ item.name }}
        </div>
        <div v-else class="nav-group">
          <div class="nav-group-title" @click="toggleMobileGroup(index.toString())">
            {{ item.name }}
            <i class="group-arrow" :class="{ expanded: expandedGroups.includes(index.toString()) }">▼</i>
          </div>
          <div v-show="expandedGroups.includes(index.toString())" class="nav-group-items">
            <div
              v-for="child in item.children"
              :key="child.path"
              class="nav-sub-item"
              @click="navigateTo(child.path!)"
            >
              {{ child.name }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </DefineTemplate>

  <!-- 巨型菜单下拉面板 - 仅桌面端显示 -->
  <div
    v-show="showMega"
    class="mega-menu-panel"
    @mouseenter="keepMegaMenu"
    @mouseleave="hideMegaMenu"
  >
    <div class="mega-menu-content">
      <div class="menu-grid">
        <div v-for="item in menuConfig.filter(m => m.children)" :key="item.name" class="menu-column">
          <h4>{{ item.name }}</h4>
          <div
            v-for="child in item.children"
            :key="child.path"
            class="flex"
          >
            <a

              :class="{ active: currentPath === child.path }"
              @click="navigateTo(child.path!)"
            >
              {{ child.name }}
            </a>
            <!-- <div class="" v-if="child.tag">{{ child.tag }}</div> -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <header class="h-20 flex items-center justify-between from-[#2c628e] to-[#5fb2ff] bg-gradient-to-r px-10 text-white max-md:px4">
    <!-- 移动端导航 -->
    <nav :class="[collapsed ? 'grid-rows-[0fr]' : 'grid-rows-[1fr]']" class="nav-menu fixed left-0 right-0 top-20 z-10 hidden from-[#2c628e] to-[#5fb2ff] bg-gradient-to-b transition-all max-md:grid">
      <div class="min-h0 overflow-hidden">
        <ReuseTemplate mode="vertical" />
      </div>
    </nav>

    <div class="header-left min-w-440px">
      <div class="flex cursor-pointer items-center gap-2" @click="router.push('/dept-client')">
        <c-icon-global-outlined max-xl:hidden class="text-12" />
        <c-icon-menu-unfold-outlined v-if="collapsed" class="hidden text-6 max-xl:block" @click="collapsed = false" />
        <c-icon-menu-fold-outlined v-else class="hidden text-6 max-xl:block" @click="collapsed = true" />
        <div class="logo-content">
          <div class="mb-1 text-6 text-white font-600 max-md:text-5">东盟国别大数据精准画像平台</div>
          <div class="text-3 text-white/85 max-md:hidden">实时追踪东盟十国300+新闻媒体及海外 600+智库，AI智能分析相关情报</div>
        </div>
      </div>
    </div>

    <div class="w-0 flex flex-1 justify-center max-xl:(fixed top-0 z--1) 2xl:mx-20">
      <nav class="nav-menu w-full">
        <ReuseTemplate mode="horizontal" />
      </nav>
    </div>

    <div class="header-right min-w-110px">
      <div class="flex items-center gap-8">
        <Message />
        <a-dropdown>
          <div class="flex cursor-pointer items-center gap-1">
            <a-avatar :size="32" class="bg-white/20 hover:bg-white/30">
              <template #icon><UserOutlined /></template>
            </a-avatar>
            {{ userInfo.active.name }}
            <CaretDownOutlined class="text-3 text-white/85 hover:text-white" />
          </div>
          <template #overlay>
            <a-menu class="min-w-40" @click="onMenuClick">
              <a-menu-item v-if="$auth([_Role.超级管理员, _Role.监测研判人员])" key="admin">
                <HomeOutlined />
                <span>管理后台</span>
              </a-menu-item>
              <a-menu-item key="profile">
                <UserOutlined />
                <span>个人信息</span>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout">
                <LogoutOutlined />
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { CaretDownOutlined, HomeOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons-vue'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Message from './Message.vue'

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()

const route = useRoute()
const router = useRouter()
const collapsed = ref(true)
const currentPath = computed(() => route?.path ?? '/dept-client')
const userInfo = useUserStore()

interface MenuItem {
  name: string
  path?: string
  tag?: '已上线' | '即将上线'
  children?: MenuItem[]
}

const menuConfig: MenuItem[] = [
  {
    name: '首页',
    path: '/dept-client',
  },
  {
    name: '东盟国别洞察',
    children: [
      // { name: '东盟精准画像', path: '/dept-client/asean-portrait' },
      { name: '东盟精准画像', path: '/dept-client/precise-portrait', tag: '即将上线' },
      { name: '东盟全景指标', path: '/dept-client/panoramic-indicator', tag: '即将上线' },
      { name: '东盟国情概况', path: '/dept-client/overview', tag: '即将上线' },
      { name: '东盟数字经济', path: '/dept-client/digital-economy', tag: '即将上线' },
      { name: '东盟新闻', path: '/dept-client/news', tag: '已上线' },
    ],
  },
  {
    name: '东盟AI前沿',
    children: [
      { name: 'AI政策新闻', path: '/dept-client/policy-news', tag: '即将上线' },
      { name: '东盟AI发展', path: '/dept-client/ai', tag: '即将上线' },
    ],
  },
  {
    name: '东盟传播动态',
    children: [
      { name: '涉我传播专题', path: '/dept-client/special-subject-me', tag: '即将上线' },
      { name: '东盟涉我新闻', path: '/dept-client/news-related-me', tag: '已上线' },
      { name: '我对东盟传播', path: '/dept-client/i-spreading-knowledge-about-asean', tag: '即将上线' },
    ],
  },
  {
    name: '全球数据监测',
    children: [
      { name: '全球海外智库', path: '/dept-client/overseas', tag: '已上线' },
      { name: '全球事件数据库', path: '/dept-client/event-database', tag: '即将上线' },
      { name: '涉我安全稳定', path: '/dept-client/event-database-me', tag: '即将上线' },
    ],
  },
  {
    name: '个人中心',
    children: [
      { name: '风险预警推送', path: '/dept-client/risk', tag: '即将上线' },
      { name: 'Ai创作工坊', path: '/dept-client/creation-workshop', tag: '即将上线' },
      { name: '我的收藏', path: '/dept-client/collect', tag: '已上线' },
    ],
  },
]

// 巨型菜单状态
const showMega = ref(false)
let hideTimer: NodeJS.Timeout | null = null

// 移动端展开的分组
const expandedGroups = ref<string[]>([])

// 判断菜单是否激活
function isMenuActive(item: any) {
  if (!item.children)
    return false
  return item.children.some((child: any) => currentPath.value === child.path)
}

function showMegaMenu() {
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
  showMega.value = true
}

function hideMegaMenu() {
  hideTimer = setTimeout(() => {
    showMega.value = false
  }, 150)
}

function keepMegaMenu() {
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

function navigateTo(path: string) {
  router.push(path)
  showMega.value = false
  collapsed.value = true
}

function toggleMobileGroup(groupKey: string) {
  const index = expandedGroups.value.indexOf(groupKey)
  if (index > -1) {
    expandedGroups.value.splice(index, 1)
  }
  else {
    expandedGroups.value.push(groupKey)
  }
}

function logout() {
  router.push('/logoutReload?api=true')
}

function onMenuClick(key: MenuInfo) {
  if (key.key === 'admin') {
    router.push('/home/')
  }
  else if (key.key === 'logout') {
    logout()
  }
  else if (key.key === 'profile') {
    router.push('/user-center/')
  }
}
</script>

<style scoped lang="less">
// 自定义导航菜单样式
.custom-nav-menu {
  display: flex;
  align-items: center;
  gap: 8px;

  .nav-item {
    position: relative;
    padding: 8px 16px;
    color: rgba(255, 255, 255, 0.85);
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    white-space: nowrap;

    &:hover {
      color: #fff;
      background: rgba(44, 99, 143, 0.4);
    }

    &.active {
      color: #fff;
      background: rgba(44, 99, 143, 0.35);
      font-weight: 500;
    }

    &.has-submenu {
      .submenu-arrow {
        font-size: 10px;
        margin-left: 4px;
        opacity: 0.7;
        transition: transform 0.3s ease;
      }
    }
  }
}

// 移动端导航样式
.mobile-nav-menu {
  padding: 16px;

  .nav-item {
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.85);
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-bottom: 8px;

    &:hover {
      color: #fff;
      background: rgba(44, 99, 143, 0.4);
    }

    &.active {
      color: #fff;
      background: rgba(44, 99, 143, 0.35);
      font-weight: 500;
    }
  }

  .nav-group {
    margin-bottom: 8px;

    .nav-group-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      color: rgba(255, 255, 255, 0.85);
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 4px;

      &:hover {
        color: #fff;
        background: rgba(44, 99, 143, 0.4);
      }

      .group-arrow {
        font-size: 10px;
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }

    .nav-group-items {
      padding-left: 16px;

      .nav-sub-item {
        padding: 8px 16px;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 4px;
        margin: 4px 0;

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          transform: translateX(4px);
        }
      }
    }
  }
}

// 巨型菜单样式
.mega-menu-panel {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, #2c628e, #5fb2ff);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  @media (max-width: 768px) {
    display: none;
  }
}

.mega-menu-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.menu-column {
  h4 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0px;
    padding-bottom: 12px;
    padding-left: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    text-align: left;
  }

  a {
    display: block;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    padding: 8px 8px;
    margin: 4px 0;
    transition: all 0.3s ease;
    cursor: pointer;
    border-radius: 4px;
    text-align: left;

    &.active,
    &:hover {
      color: #fff;
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(2px);
    }
  }
}
</style>

<style  lang="less">
.ch2-main:has(.custom-head-top) {
  width: 100%;
  padding: 0;
}
</style>
