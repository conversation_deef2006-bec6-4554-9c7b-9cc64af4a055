<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <c-pro-table
        ref="tableRef"
        :api="Api.ChartStatistices.GetDatasetsAsync"
        :columns="columns"
        :serial-number="true"
        row-key="id"
        immediate
        bordered
        operation
      >
        <template #header>
          <a-button type="primary" @click="openEditModal()">新增数据集</a-button>
        </template>
        <template #operation="{ record }">
          <a @click="openEditModal(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除该数据集？" @confirm="deleteDataset(record)">
            <a>删除</a>
          </a-popconfirm>
        </template>
      </c-pro-table>
    </div>
    <!-- 新增/编辑弹窗 -->
    <c-modal :open="editVisible" width="1400px" :closable="false" full-modal>
      <template #title>
        <span v-if="isSaved">[未保存]</span> 数据集编辑
      </template>
      <template #footer>
        <a-button @click="cancelEdit">关闭</a-button>
        <a-button type="primary" ghost @click="handleEditOk(true)">保存并关闭</a-button>
        <a-button type="primary" @click="handleEditOk()">保存</a-button>
      </template>
      <div class="mb4 rounded bg-primary-bg p4">
        <a-row :gutter="16">
          <a-col :span="8">
            ·在“表名”输入框中修改数据表的名称
          </a-col>
          <a-col :span="8">
            ·“目录”为数据表的目录
          </a-col>
          <a-col :span="8">
            ·点击表格中的单元格可以直接编辑数据内容
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            ·指标值前有 <span class="inline-block rounded-full bg-green-4 p1.5 text-base" /> 绿色标记的为国家官方公布
          </a-col>
          <a-col :span="8">
            ·指标值有  new  标记的为数据更新于最近3个月
          </a-col>
        </a-row>
      </div>

      <div class="mb4 rounded bg-primary-bg p4 !pb0">
        <a-form :model="editForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="表名" required>
                <a-input v-model:value="editForm.name" placeholder="数据集名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="目录">
                <a-input v-model:value="editForm.describe" placeholder="备注" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 指标模式切换 -->
      <div class="my-4 border border-blue-200 rounded bg-blue-50 p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <a-switch
              :checked="isIndicatorMode"
              @change="handleIndicatorModeChange"
            />
            <span class="text-sm font-medium">指标模式</span>
            <a-tooltip title="开启后字段将固定为：国家、指标值、透视值，数据将以透视表格式显示">
              <i class="i-mdi-help-circle-outline cursor-help text-gray-400" />
            </a-tooltip>
          </div>
          <a-tag v-if="isIndicatorMode" color="blue">透视表模式</a-tag>
        </div>
        <div v-if="isIndicatorMode" class="mt-2 text-xs text-blue-600">
          指标模式下，数据将以透视表格式显示：第一列为透视值，后续列为各国家，单元格为指标值
        </div>
      </div>
      <a-tabs v-model:active-key="activeTab" class="mt-2">
        <a-tab-pane key="fields" tab="字段定义">
          <!-- Excel导入区域 -->
          <div v-if="!isIndicatorMode" class="mb-4 rounded bg-gray-50 p-3">
            <div class="mb-2 flex items-center gap-2">
              <span class="text-sm font-medium">快速导入：</span>
              <a-upload
                :before-upload="handleExcelUpload"
                :show-upload-list="false"
                accept=".xlsx,.xls"
              >
                <a-button type="primary" size="small">
                  <template #icon><i class="i-mdi-file-excel text-green-600" /></template>
                  从Excel导入
                </a-button>
              </a-upload>
              <span class="text-xs text-gray-500">支持.xlsx和.xls格式，第一行作为字段名，自动生成字段和数据</span>
            </div>
            <div v-if="excelFileName" class="text-sm text-green-600">
              已导入文件：{{ excelFileName }}
            </div>
          </div>
          <a-table
            ref="fieldsTableRef"
            :data-source="fieldsJson"
            :columns="fieldColumns"
            :pagination="false"
            bordered
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'drag'">
                <div class="i-mdi-drag-vertical cursor-move text-gray-400 hover:text-gray-600" />
              </template>
              <template v-else-if="column.dataIndex === 'type'">
                <a-select v-model:value="record['type']" style="width: 100px">
                  <a-select-option :value="0">字符串</a-select-option>
                  <a-select-option :value="1">数字</a-select-option>
                  <a-select-option :value="2">日期</a-select-option>
                  <a-select-option :value="3">布尔</a-select-option>
                </a-select>
              </template>
              <template v-else-if="column.dataIndex === 'isIndicator'">
                <a-switch
                  v-model:checked="record['isIndicator']"
                  size="small"
                  :disabled="!record['isEdit']"
                />
              </template>
              <template v-else-if="column.key === 'action' ">
                <a v-if="record['isEdit']" style="color: red" @click="removeField(index)">删除</a>
              </template>
              <template v-else-if="column.dataIndex">
                <a-input
                  :key="`${index}_${column.dataIndex}`"
                  v-model:value="fieldsJson[index]![column.dataIndex as keyof ChartFieldDefinition]"
                  :disabled="!record['isEdit']"
                />
              </template>
            </template>
          </a-table>
          <a-button v-if="!isIndicatorMode" class="mt-2" type="dashed" block @click="addField">+ 添加字段</a-button>
        </a-tab-pane>
        <a-tab-pane key="data" tab="数据录入" :disabled="fieldsJson.length === 0">
          <div v-if="fieldsJson.length === 0" class="py-8 text-center text-gray-400">
            请先定义字段后再录入数据
          </div>
          <template v-else>
            <!-- 批量操作工具栏 -->
            <div class="batch-toolbar mb-3 flex items-center justify-between">
              <div class="flex items-center gap-2">
                <a-button type="primary" @click="addRow">+ 添加数据行</a-button>
                <a-button v-if="!isIndicatorMode" @click="showBatchInputModal = true">批量录入</a-button>
                <a-button
                  v-if="!isIndicatorMode"
                  danger
                  :disabled="selectedRowKeys.length === 0"
                  @click="batchDeleteRows"
                >
                  批量删除 ({{ selectedRowKeys.length }})
                </a-button>
              </div>
              <div class="flex items-center gap-4">
                <div class="text-sm text-gray-500">
                  共 {{ records.length }} 条数据
                </div>
              </div>
            </div>

            <!-- 普通模式表格视图 -->
            <div v-if="!isIndicatorMode && viewMode === 'table'">
              <a-table
                :data-source="recordsWithIndex"
                :columns="dataColumns"
                :row-selection="{
                  selectedRowKeys,
                  onChange: onSelectChange,
                  type: 'checkbox',
                }"
                bordered
                size="small"
                :scroll="{ x: tableScrollWidth }"
                row-key="index"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'order'">
                    <div class="flex flex-col gap-1">
                      <a-button
                        size="small"
                        type="text"
                        :disabled="index === 0"
                        @click="moveRowUp(index)"
                      >
                        <div class="i-mdi-chevron-up text-sm" />
                      </a-button>
                      <a-button
                        size="small"
                        type="text"
                        :disabled="index === records.length - 1"
                        @click="moveRowDown(index)"
                      >
                        <div class="i-mdi-chevron-down text-sm" />
                      </a-button>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <div class="flex gap-1">
                      <a @click="editRow(index)">编辑</a>
                      <a-divider type="vertical" />
                      <a style="color: red" @click="removeRow(index)">删除</a>
                    </div>
                  </template>
                  <template v-else>
                    <IndicatorHistoryPopover
                      v-if="fieldsJson[Number(column.dataIndex)]?.isIndicator"
                      :history="getCellHistory(record.index, Number(column.dataIndex))"
                      :display-value="record[column.dataIndex!]"
                      @cell-click="openIndicatorModal(record.index, Number(column.dataIndex))"
                      @view-source="(cell) => showSpecificIndicatorDetail(record.index, Number(column.dataIndex), cell)"
                    />
                    <component
                      :is="getCellComponent(column)"
                      v-else
                      :value="record[column.dataIndex!]"
                      class="min-w-24"
                      @update:value="(value) => updateCellValue(record.index, Number(column.dataIndex), value)"
                    />
                  </template>
                </template>
              </a-table>
            </div>

            <!-- 指标模式透视表 -->
            <div v-if="isIndicatorMode" class="indicator-pivot-table">
              <a-table
                :data-source="pivotTableData"
                :columns="pivotTableColumns"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: pivotTableScrollWidth }"
                row-key="index"
              >
                <template #bodyCell="{ column, record }">
                  <!-- 透视值列（第一列） -->
                  <template v-if="column.dataIndex === '0'">
                    <div class="pivot-value-cell">
                      <span class="text-gray-700 font-medium">{{ record[column.dataIndex] }}</span>
                    </div>
                  </template>

                  <!-- 操作列 -->
                  <template v-else-if="column.key === 'action'">
                    <div class="flex justify-center gap-1">
                      <a-button
                        size="small"
                        type="text"
                        :disabled="record.index === 'pivot_0'"
                        @click="movePivotRow(record.pivotValue, 'up')"
                      >
                        <template #icon><UpOutlined /></template>
                      </a-button>
                      <a-button
                        size="small"
                        type="text"
                        :disabled="record.index === `pivot_${pivotTableData.length - 1}`"
                        @click="movePivotRow(record.pivotValue, 'down')"
                      >
                        <template #icon><DownOutlined /></template>
                      </a-button>
                      <a-popconfirm
                        title="确定删除该行数据？"
                        @confirm="deletePivotRow(record.pivotValue)"
                      >
                        <a-button size="small" type="text" danger>
                          <template #icon><DeleteOutlined /></template>
                        </a-button>
                      </a-popconfirm>
                    </div>
                  </template>

                  <!-- 国家指标列 -->
                  <template v-else>
                    <IndicatorHistoryPopover
                      :history="getPivotCellHistory(record, column)"
                      :display-value="record[column.dataIndex]"
                      @cell-click="handleIndicatorCellClick(record, column)"
                      @view-source="(cell) => showSpecificPivotIndicatorDetail(record, column, cell)"
                    />
                  </template>
                </template>
              </a-table>
            </div>
          </template>
        </a-tab-pane>
      </a-tabs>
    </c-modal>

    <!-- 批量录入弹窗 -->
    <a-modal
      v-model:open="showBatchInputModal"
      title="批量录入数据"
      width="600px"
      @ok="handleBatchInput"
      @cancel="showBatchInputModal = false"
    >
      <div class="space-y-4">
        <div>
          <div class="mb-2 text-sm font-medium">数据格式说明：</div>
          <div class="text-xs text-gray-500 space-y-1">
            <div>• 每行代表一条数据记录</div>
            <div>• 字段之间用逗号(,)、制表符(\t)或分号(;)分隔</div>
            <div>• 字段顺序：{{ fieldsJson.map(f => f.name).join(' → ') }}</div>
          </div>
        </div>

        <div>
          <div class="mb-2 text-sm font-medium">批量数据：</div>
          <a-textarea
            v-model:value="batchInputText"
            placeholder="请输入数据，例如：
中国,100,2024-01-01
美国,200,2024-01-02
日本,150,2024-01-03"
            :rows="10"
            class="text-sm font-mono"
          />
        </div>

        <div class="text-xs text-gray-400">
          提示：粘贴Excel数据时会自动识别分隔符
        </div>
      </div>
    </a-modal>

    <!-- 编辑数据弹窗 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑数据"
      width="600px"
      @ok="handleEditSave"
      @cancel="showEditModal = false"
    >
      <div class="space-y-4">
        <div
          v-for="(field, fieldIndex) in fieldsJson"
          :key="fieldIndex"
          class="flex items-center gap-4"
        >
          <div class="w-20 text-sm text-gray-600 font-medium">{{ field.name }}:</div>
          <div class="flex-1">
            <component
              :is="getCellComponent({ dataIndex: String(fieldIndex), type: field.type })"
              v-model:value="editingData[fieldIndex]"
              style="width: 100%"
            />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 指标单元格编辑弹窗 -->
    <a-modal
      v-model:open="showIndicatorModal"
      title="新增指标数据"
      width="700px"
      @ok="handleIndicatorSave"
      @cancel="showIndicatorModal = false"
    >
      <div class="space-y-4">
        <IndicatorForm v-model:value="editingIndicatorCell" />
      </div>
    </a-modal>

    <!-- 指标数据详情查看弹窗 -->
    <a-modal
      v-model:open="showIndicatorDetailModal"
      title="指标数据详情"
      width="800px"
      :footer="null"
    >
      <div class="indicator-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="国家">
              {{ indicatorDetailData.country }}（  {{ indicatorDetailData.pivotValue }}）
            </a-descriptions-item>
            <a-descriptions-item :label="fieldsJson[Number(fieldIndexes.pivot)]?.name || '透视值'">
              <span class="text-lg text-green-600 font-medium">
                {{ indicatorDetailData.currentValue }}
              </span>
            </a-descriptions-item>

            <a-descriptions-item :span="2" label="更新类型">
              <Upload :show-upload="false" :value="indicatorDetailData.attachments" auto-list />
            </a-descriptions-item>

            <a-descriptions-item label="数据来源">
              <a-tag :color="getViaColor(indicatorDetailData.currentVia)">
                {{ indicatorDetailData.currentVia }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最后更新">
              {{ dateTime(indicatorDetailData.lastUpdated) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新类型">
              {{ indicatorDetailData.updateType }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="detail-section">
          <h3 class="section-title">溯源记录</h3>
          <div class="history-timeline px2">
            <div
              v-for="(version, index) in indicatorDetailData.history"
              :key="index"
              class="timeline-item cursor-pointer"
              :class="{ 'is-current': index === indicatorDetailData.index }"
              @click="switchToHistoryVersion(version)"
            >
              <div class="timeline-marker" />
              <div class="timeline-content">
                <div class="mb-2 flex items-start justify-between">
                  <div>
                    <a-tag :color="getViaColor(version.via)" size="small">
                      {{ version.via || '未知来源' }}
                    </a-tag>
                    <span class="ml-2 text-sm text-gray-500">
                      {{ dateTime(version.updatedAt) }}
                    </span>
                  </div>
                  <div class="flex gap-1">
                    <a-button
                      size="small"
                      type="text"
                      @click.stop="editHistoryRecord(version, index)"
                    >
                      编辑
                    </a-button>
                    <a-popconfirm
                      title="确定删除该历史记录？"
                      @confirm="deleteHistoryRecord(version, index)"
                      @click.stop
                    >
                      <a-button size="small" type="text" danger>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </div>
                </div>
                <div class="text-lg font-medium" :class="index === 0 ? 'text-green-600' : 'text-gray-600'">
                  {{ version.value }}
                </div>
              </div>
              <div class="mb-1 text-sm text-gray-600">
                更新类型：{{ version.updateType ?? '未指定更新类型' }}
              </div>
              <div v-if="version.remarks" class="text-sm text-gray-700">
                {{ version.remarks }}
              </div>
              <div v-if="version.url" class="mt-1 text-sm">
                <a :href="version.url" target="_blank" class="text-blue-500 hover:text-blue-700">
                  {{ version.url }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 编辑历史记录弹窗 -->
    <a-modal
      v-model:open="showEditHistoryModal"
      title="编辑历史记录"
      width="600px"
      @ok="saveHistoryRecord"
      @cancel="cancelEditHistory"
    >
      <IndicatorForm v-model:value="editingHistory" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { ChartDatasetView } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { ChartDatasetCell } from '@/.generated/models/ChartDatasetCell'
import { ChartFieldDefinition } from '@/.generated/models/ChartFieldDefinition'
import { Api } from '@/api'
import { ChartFieldType, DatasetSourceType } from '@/api/models'
import { DeleteOutlined, DownOutlined, UpOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { Switch as CSwitch, DatePicker, Input, InputNumber } from 'ch2-components/lib'
import StandardSelect from 'ch2-components/lib/standard/src/StandardSelect.vue'
import Sortable from 'sortablejs'
import { computed, h, nextTick, ref, watch } from 'vue'
import * as XLSX from 'xlsx'
import IndicatorForm from './components/IndicatorForm.vue'
import IndicatorHistoryPopover from './components/IndicatorHistoryPopover.vue'
import { EMPTY_PLACEHOLDER, getViaColor, INDICATOR_FIELDS, useIndicatorData } from './composables/useIndicatorData'

definePage({
  meta: {
    layout: 'admin',
    title: '数据集',
    local: true,
    icon: 'DatabaseOutlined',
    order: 3,
  },
})

const { byKeyApi } = useStandard()

const isSaved = ref(true)

const tableRef = ref<any>(null)
const fieldsTableRef = ref<any>(null)

const columns = ref<ColumnProps[]>([
  { dataIndex: 'name', title: '表名', key: 'name', align: 'center' as const, search: {
    el: 'input',
    method: 'GET',
    attrs: { placeholder: '请输入数据集名称' },
  } },
  // {
  //   dataIndex: 'sourceType',
  //   title: '来源类型',
  //   key: 'sourceType',
  //   align: 'center' as const,
  //   enum: DatasetSourceType,
  //   customRender: ({ text }: any) => ['手动录入', 'Excel导入', 'API对接', 'SQL'][Number(text)] || text,
  // },
  { dataIndex: 'updatedAt', title: '更新时间', dateFormat: true, key: 'updatedAt', align: 'center' as const },
  { dataIndex: 'describe', title: '目录', key: 'describe', align: 'center' as const },
  // { dataIndex: 'url', title: '数据来源URL', key: 'url', align: 'center' as const, ellipsis: true },
  // { dataIndex: 'remarks', title: '采集说明', key: 'remarks', align: 'center' as const },
])

const editVisible = ref(false)
const editForm = ref<Partial<ChartDatasetView>>({})
const fieldsJson = ref<ChartFieldDefinition[]>(deepCopy(INDICATOR_FIELDS))
// records现在是三维数组 ChartDatasetCell[][][]
const records = ref<ChartDatasetCell[][][]>([])
const activeTab = ref('fields')
// Excel导入相关
const excelFileName = ref('')
// 批量操作相关
const selectedRowKeys = ref<number[]>([])
const showBatchInputModal = ref(false)
const batchInputText = ref('')
// 视图模式
const viewMode = ref<'table' | 'card'>('table')

// 指标模式 - 根据字段定义自动判断
const isIndicatorMode = ref(true)

// 编辑相关
const showEditModal = ref(false)
const editingIndex = ref(-1)
const editingData = ref<any[]>([])
// 指标单元格编辑相关
const showIndicatorModal = ref(false)
const editingIndicatorCell = ref<ChartDatasetCell>(new ChartDatasetCell())
const editingCellPosition = ref({ rowIndex: -1, fieldIndex: -1 })

watch(editVisible, (v) => {
  if (v) {
    nextTick(() => {
      initFieldsSortable()
    })
  }
})

watch([fieldsJson, records], () => {
  isSaved.value = false
}, { deep: true })

// 注意：不要监听fieldsJson变化来重新初始化拖拽，会导致循环

// 监听选项卡切换，初始化对应的拖拽功能
watch(activeTab, (newTab) => {
  nextTick(() => {
    if (newTab === 'fields') {
      initFieldsSortable()
    }
  })
})

function confirmFieldChange(action: () => void) {
  if (records.value.length > 0) {
    Modal.confirm({
      title: '字段变更会影响已有数据，是否继续？',
      onOk: action,
    })
  }
  else {
    action()
  }
}

// 处理指标模式切换
function handleIndicatorModeChange(checked: boolean) {
  if (checked) {
    // 切换到指标模式 - 保持数据完整性
    if (isValidIndicatorModeData()) {
      // 数据已经符合指标模式，直接切换
      message.success('已切换到指标模式')
      isIndicatorMode.value = true
    }
    else {
      message.error('当前数据不符合指标模式要求，已自动调整字段和数据格式')
    }
  }
  else {
    // 切换到普通模式 - 保持数据完整性
    convertToNormalMode()
    message.success('已切换到普通模式')
    isIndicatorMode.value = false
  }
}

// 检查当前数据是否符合指标模式
function isValidIndicatorModeData(): boolean {
  // 检查字段定义是否符合指标模式
  if (fieldsJson.value.length !== 3)
    return false

  const hasCountryField = fieldsJson.value.some(f => f.name === '国家' && !f.isIndicator)
  const hasIndicatorField = fieldsJson.value.some(f => f.isIndicator)

  return hasCountryField && hasIndicatorField
}

// 转换为普通模式
function convertToNormalMode() {
  // 如果没有字段，添加默认的国家字段
  if (fieldsJson.value.length === 0) {
    fieldsJson.value = [
      Object.assign(new ChartFieldDefinition(), {
        name: '国家',
        type: 0,
        unit: null,
        isEdit: false,
        isIndicator: false,
        description: '内置字段，所属地区，如省、市、区等',
      }),
    ]
  }
}

// Excel导入处理函数
function handleExcelUpload(file: File) {
  excelFileName.value = file.name

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      if (!firstSheetName) {
        message.warning('Excel文件中没有找到工作表')
        return
      }
      const worksheet = workbook.Sheets[firstSheetName]
      if (!worksheet) {
        message.warning('无法读取Excel工作表')
        return
      }

      // 转换为JSON数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]
      console.log('Excel数据:', jsonData)
      if (jsonData.length === 0) {
        message.warning('Excel文件为空')
        return
      }

      // 第一行作为字段名
      const headers = jsonData[0] as string[]
      const dataRows = jsonData.slice(1)

      // 自动推断字段类型并生成字段定义
      const newFields: ChartFieldDefinition[] = []

      // 保留国家字段
      // newFields.push(Object.assign(new ChartFieldDefinition(), {
      //   name: '国家',
      //   type: 0, // String
      //   unit: null,
      //   isEdit: false,
      //   description: '内置字段，所属地区，如省、市、区等',
      // }))

      // 添加Excel中的字段
      headers.forEach((header, index) => {
        if (header && header.trim()) {
          // 自动推断字段类型
          let fieldType = 0 // 默认字符串

          // 检查该列的数据类型
          const columnData = dataRows.map(row => row[index]).filter(val => val !== undefined && val !== null && val !== '')

          if (columnData.length > 0) {
            // 检查是否为数字类型
            const isNumeric = columnData.every(val => !Number.isNaN(Number(val)) && Number.isFinite(Number(val)))
            if (isNumeric) {
              fieldType = 1 // 数字
            }
            // 检查是否为日期类型
            else {
              const isDate = columnData.some((val) => {
                const dateVal = new Date(val)
                return !Number.isNaN(dateVal.getTime()) && val.toString().match(/\d{4}[-/]\d{1,2}[-/]\d{1,2}/)
              })
              if (isDate) {
                fieldType = 2 // 日期
              }
            }
          }

          newFields.push(Object.assign(new ChartFieldDefinition(), {
            name: header.trim(),
            type: fieldType,
            unit: null,
            isEdit: true,
            isIndicator: false,
            description: '',
          }))
        }
      })

      // 更新字段定义
      fieldsJson.value = newFields

      // 转换数据行为三维数组
      const newRecords: ChartDatasetCell[][][] = []
      dataRows.forEach((row) => {
        const recordRow: ChartDatasetCell[][] = []

        // 按照字段顺序填充数据
        newFields.forEach((field) => {
          if (field.name === '国家') {
            // 国家字段设为空，用户可以手动填写
            field.isEdit = false
          }

          // 找到对应的Excel列索引
          const excelColumnIndex = headers.findIndex(h => h && h.trim() === field.name)
          let value: any = ''

          if (excelColumnIndex >= 0) {
            value = row[excelColumnIndex]

            // 根据字段类型转换值
            if (field.type === 1 && value !== undefined && value !== null && value !== '') {
              // 数字类型
              value = Number(value)
            }
            else if (field.type === 2 && value !== undefined && value !== null && value !== '') {
              // 日期类型，保持原始格式
              value = value.toString()
            }
            else {
              // 字符串类型
              value = value ? value.toString() : ''
            }
          }

          // 创建 ChartDatasetCell
          const cell = new ChartDatasetCell()
          cell.value = value || ''
          cell.updatedAt = dayjs()
          recordRow.push([cell])
        })

        newRecords.push(recordRow)
      })

      // 更新数据
      records.value = newRecords

      message.success(`成功导入 ${newRecords.length} 条数据，${newFields.length - 1} 个字段`)

      // 切换到数据录入标签页
      activeTab.value = 'data'
    }
    catch (error) {
      console.error('Excel解析错误:', error)
      message.error('Excel文件解析失败，请检查文件格式')
    }
  }

  reader.readAsArrayBuffer(file)

  // 阻止默认上传行为
  return false
}

function openEditModal(record?: ChartDatasetView) {
  if (record) {
    editForm.value = { ...record }
    fieldsJson.value = Array.isArray(record.fieldsJson) ? JSON.parse(JSON.stringify(record.fieldsJson)) : []
    // records现在是三维数组，需要确保数据格式正确
    records.value = record.records ?? []
    isIndicatorMode.value = fieldsJson.value.some(field => field.isIndicator === true)
  }
  else {
    editForm.value = { sourceType: DatasetSourceType.Manual }
    // convertToIndicatorMode()
    fieldsJson.value = deepCopy(INDICATOR_FIELDS)
    records.value = []
    isIndicatorMode.value = true
  }

  activeTab.value = fieldsJson.value.length > 0 ? 'data' : 'fields'

  // 重置Excel文件名
  excelFileName.value = ''
  editVisible.value = true
  nextTick(() => {
    isSaved.value = true
  })
}

function addField() {
  confirmFieldChange(() => {
    fieldsJson.value.push({ ...new ChartFieldDefinition(), isEdit: true, isIndicator: false, name: '' })
    syncRecordsWithFields()
    // 重新初始化拖拽功能
    nextTick(() => {
      initFieldsSortable()
    })
  })
}
function removeField(index: number) {
  confirmFieldChange(() => {
    fieldsJson.value.splice(index, 1)
    syncRecordsWithFields()
    // 重新初始化拖拽功能
    nextTick(() => {
      initFieldsSortable()
    })
  })
}

const fieldColumns = ref([
  { title: '', key: 'drag', width: 40, align: 'center' as const },
  { title: '字段名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '单位', dataIndex: 'unit', key: 'unit' },
  { title: '是否指标', dataIndex: 'isIndicator', key: 'isIndicator', width: 80, align: 'center' as const },
  { title: '备注', dataIndex: 'description', key: 'description' },
  { title: '操作', key: 'action' },
])

function syncRecordsWithFields() {
  // 保证每行数据的字段和fieldsJson一致（三维数组格式）
  records.value = records.value.map((row: ChartDatasetCell[][]) => {
    const newRow: ChartDatasetCell[][] = []
    fieldsJson.value.forEach((field, index) => {
      // 保持原有数据，如果索引超出范围则创建新的 ChartDatasetCell
      if (row[index]) {
        newRow.push(row[index])
      }
      else {
        const cell = new ChartDatasetCell()
        cell.value = ''
        cell.updatedAt = dayjs()
        newRow.push([cell])
      }
    })
    return newRow
  })
}

watch(fieldsJson, syncRecordsWithFields, { deep: true })

// 预定义的国家列表（可以从配置或API获取）
const predefinedCountries = ref<string[]>([])

// 使用指标数据管理hook
const {
  showIndicatorDetailModal,
  indicatorDetailData,
  getCellValue,
  getCellHistory,
  getPivotCellHistory,
  showSpecificIndicatorDetail,
  showSpecificPivotIndicatorDetail,
  pivotTableData,
  pivotTableColumns,
  fieldIndexes,
  findDataPosition,
  switchToHistoryVersion,
  deletePivotRow,
  movePivotRow,
} = useIndicatorData(records, fieldsJson, predefinedCountries, isIndicatorMode)

// 为表格数据添加索引
const recordsWithIndex = computed(() => {
  if (isIndicatorMode.value) {
    return pivotTableData.value
  }

  return records.value.map((record, index) => {
    const rowData: any = { index }
    // 将数组数据转换为对象格式，以字段索引作为key
    record.forEach((cellArray, fieldIndex) => {
      if (cellArray && cellArray.length > 0) {
        // 获取最新的记录（按更新时间排序）
        const sortedCells = cellArray.sort((a, b) => !b.updatedAt ? Number.MIN_VALUE : dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
        rowData[String(fieldIndex)] = sortedCells[0]?.value ?? ''
      }
      else {
        rowData[String(fieldIndex)] = ''
      }
    })
    return rowData
  })
})

// 计算表格滚动宽度
const tableScrollWidth = computed(() => {
  let totalWidth = 60 + 80 // 顺序列 + 操作列
  fieldsJson.value.forEach((field) => {
    totalWidth += getColumnWidth(field.type)
  })
  return Math.max(totalWidth, 800) // 最小宽度800px
})

// 计算透视表滚动宽度
const pivotTableScrollWidth = computed(() => {
  return Math.max(pivotTableColumns.value.length * 120, 800)
})

const dataColumns = computed(() => {
  if (isIndicatorMode.value) {
    return pivotTableColumns.value
  }

  const cols: any[] = []

  // 添加顺序调整列
  cols.push({
    title: '顺序',
    key: 'order',
    width: 60,
    align: 'center' as const,
    fixed: 'left' as const,
  })

  // 添加字段列
  fieldsJson.value.forEach((f: any, index: number) => {
    cols.push({
      title: f.name,
      dataIndex: String(index), // 使用字符串索引作为dataIndex
      key: String(index),
      type: f.type,
      width: getColumnWidth(f.type), // 根据字段类型设置宽度
    })
  })

  // 添加操作列
  cols.push({
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    type: undefined,
    width: 80,
    fixed: 'right' as const,
  })
  return cols
})

function addRow() {
  if (isIndicatorMode.value) {
    // 指标模式下，添加一个新的透视值行，用户可以直接在表格中编辑
    addIndicatorRow()
  }
  else {
    // 普通模式下，创建新行
    const row: ChartDatasetCell[][] = []
    fieldsJson.value.forEach((field) => {
      const cell = new ChartDatasetCell()
      cell.value = field.isIndicator ? EMPTY_PLACEHOLDER : ''
      cell.updatedAt = dayjs()
      row.push([cell])
    })
    records.value.push(row)
  }
}

// 指标模式下添加新行
function addIndicatorRow() {
  // 使用 Modal 输入透视值
  const pivotValue = ref('')
  Modal.confirm({
    title: '添加新的透视值',
    content: h('div', [
      h('p', '请输入透视值（如年份、季度等）：'),
      h(Input, {
        'onUpdate:value': v => pivotValue.value = v,
        'placeholder': '例如：2024、Q1、第一季度',
        'style': { marginTop: '8px' },
      }),
    ]),
    onOk() {
      if (!pivotValue.value) {
        message.error('请输入透视值')
        return Promise.reject(new Error('透视值不能为空'))
      }

      // 检查是否已存在相同的透视值 - 简化版本
      const indexes = fieldIndexes.value
      const existingPivotValues = new Set<string>()
      records.value.forEach((record) => {
        const existing = getCellValue(record[indexes.pivot])
        if (existing) {
          existingPivotValues.add(existing)
        }
      })

      if (existingPivotValues.has(pivotValue.value)) {
        message.warning('该透视值已存在，请使用不同的透视值')
        return Promise.reject(new Error('透视值已存在'))
      }

      // 为每个预定义国家创建一条记录 - 简化版本
      predefinedCountries.value.forEach((country) => {
        const row: ChartDatasetCell[][] = []

        // 按字段索引顺序创建数据
        fieldsJson.value.forEach((field, index) => {
          const cell = new ChartDatasetCell()
          cell.updatedAt = dayjs()

          if (index === indexes.country) {
            cell.value = country
          }
          else if (index === indexes.indicator) {
            cell.value = EMPTY_PLACEHOLDER
          }
          else if (index === indexes.pivot) {
            cell.value = pivotValue.value
          }
          else {
            cell.value = ''
          }

          row[index] = [cell]
        })

        records.value.push(row)
      })
      console.log('records', records.value)
      message.success(`已添加透视值"${pivotValue.value}"的数据行，请在表格中填写各国家的指标值`)
      return Promise.resolve()
    },
  })
}
function removeRow(index: number) {
  records.value.splice(index, 1)
  // 更新选中状态
  selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== index)
    .map(key => key > index ? key - 1 : key)
}

// 行选择相关
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量删除
function batchDeleteRows() {
  if (selectedRowKeys.value.length === 0)
    return

  // 按索引倒序排列，从后往前删除，避免索引变化
  const sortedIndexes = [...selectedRowKeys.value].sort((a, b) => b - a)
  sortedIndexes.forEach((index) => {
    records.value.splice(index, 1)
  })

  selectedRowKeys.value = []
  message.success(`已删除 ${sortedIndexes.length} 条数据`)
}

// 上移数据行
function moveRowUp(index: number) {
  if (index <= 0)
    return

  const temp = records.value[index]
  records.value[index] = records.value[index - 1]!
  records.value[index - 1] = temp!
}

// 下移数据行
function moveRowDown(index: number) {
  if (index >= records.value.length - 1)
    return

  const temp = records.value[index]
  records.value[index] = records.value[index + 1]!
  records.value[index + 1] = temp!
}

// 编辑行数据
function editRow(index: number) {
  editingIndex.value = index
  // 提取每个单元格的最新值用于编辑
  editingData.value = records.value[index].map((cellArray) => {
    if (cellArray && cellArray.length > 0) {
      // 获取最新的记录
      const sortedCells = cellArray.sort((a, b) => dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
      return sortedCells[0]?.value ?? ''
    }
    return ''
  })
  showEditModal.value = true
}

// 保存编辑
function handleEditSave() {
  if (editingIndex.value >= 0) {
    // 将编辑的值转换回 ChartDatasetCell 格式
    const newRow: ChartDatasetCell[][] = []
    editingData.value.forEach((value, fieldIndex) => {
      // 获取最新的记录作为基础
      const cellArray = records.value[editingIndex.value]?.[fieldIndex] || []
      let existingCell: ChartDatasetCell | undefined

      if (cellArray.length > 0) {
        const sortedCells = cellArray.sort((a, b) => dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
        existingCell = sortedCells[0]
      }

      const cell = existingCell ? { ...existingCell } : new ChartDatasetCell()
      cell.value = value
      cell.updatedAt = dayjs()

      newRow.push([cell])
    })

    records.value[editingIndex.value] = newRow
    showEditModal.value = false
    message.success('数据已更新')
  }
}

// 更新单元格值
function updateCellValue(rowIndex: number, fieldIndex: number, value: any) {
  if (rowIndex >= 0 && rowIndex < records.value.length && fieldIndex >= 0 && fieldIndex < fieldsJson.value.length) {
    const field = fieldsJson.value[fieldIndex]
    if (field.isIndicator) {
      // 指标字段：点击打开编辑模态框
      openIndicatorModal(rowIndex, fieldIndex)
    }
    else {
      // 普通字段：直接更新值
      // 确保数据结构存在
      if (!records.value[rowIndex]) {
        records.value[rowIndex] = []
      }
      if (!records.value[rowIndex][fieldIndex]) {
        records.value[rowIndex][fieldIndex] = []
      }

      const existingCell = records.value[rowIndex][fieldIndex][0]
      const cell = existingCell ? { ...existingCell } : new ChartDatasetCell()
      cell.value = value
      cell.updatedAt = dayjs()
      records.value[rowIndex][fieldIndex] = [cell]
    }
  }
}

// 打开指标编辑模态框
function openIndicatorModal(rowIndex: number, fieldIndex: number) {
  editingCellPosition.value = { rowIndex, fieldIndex }

  // 获取当前单元格的最新数据
  const cellHistory = getCellHistory(rowIndex, fieldIndex)
  const currentCell = cellHistory[0] // 最新的记录

  if (currentCell) {
    editingIndicatorCell.value = { ...currentCell }
  }
  else {
    editingIndicatorCell.value = new ChartDatasetCell()
    editingIndicatorCell.value.updatedAt = dayjs()
  }

  showIndicatorModal.value = true
}

// 保存指标数据
function handleIndicatorSave() {
  const { rowIndex, fieldIndex } = editingCellPosition.value
  if (rowIndex >= 0 && fieldIndex >= 0) {
    // 确保数据结构存在
    if (!records.value[rowIndex]) {
      records.value[rowIndex] = []
    }
    if (!records.value[rowIndex][fieldIndex]) {
      records.value[rowIndex][fieldIndex] = []
    }

    // 创建新的指标数据记录
    const cellToSave = { ...editingIndicatorCell.value }
    cellToSave.updatedAt = dayjs()

    // 插入到数组开头（最新的记录在前面）
    records.value[rowIndex][fieldIndex].unshift(cellToSave)

    showIndicatorModal.value = false
    message.success('指标数据已保存')
  }
}

// 处理指标模式下的单元格点击 - 简化版本
function handleIndicatorCellClick(record: any, column: any) {
  if (!isIndicatorMode.value)
    return

  const columnIndex = Number(column.dataIndex)
  if (columnIndex === 0)
    return // 透视值列不可编辑

  const countryName = predefinedCountries.value[columnIndex - 1]
  const pivotValue = record.pivotValue

  if (!countryName || !pivotValue)
    return

  // 使用重构后的查找函数
  let position = findDataPosition(countryName, pivotValue)

  if (!position) {
    // 创建新的数据记录
    const indexes = fieldIndexes.value
    const newRow: ChartDatasetCell[][] = []

    // 按字段索引顺序创建数据
    fieldsJson.value.forEach((field, index) => {
      const cell = new ChartDatasetCell()
      cell.updatedAt = dayjs()

      if (index === indexes.country) {
        cell.value = countryName
      }
      else if (index === indexes.indicator) {
        cell.value = EMPTY_PLACEHOLDER
        cell.via = '手动录入'
        cell.updateType = '新指标'
      }
      else if (index === indexes.pivot) {
        cell.value = pivotValue
      }
      else {
        cell.value = ''
      }

      newRow[index] = [cell]
    })

    records.value.push(newRow)
    position = {
      rowIndex: records.value.length - 1,
      fieldIndex: indexes.indicator,
      country: countryName,
      pivotValue,
    }
  }

  // 打开指标编辑模态框
  openIndicatorModal(position.rowIndex, position.fieldIndex)
}

// 批量录入处理
function handleBatchInput() {
  if (!batchInputText.value.trim()) {
    message.warning('请输入批量数据')
    return
  }

  try {
    const lines = batchInputText.value.trim().split('\n')
    const newRecords: ChartDatasetCell[][][] = []

    lines.forEach((line) => {
      if (!line.trim())
        return // 跳过空行

      // 尝试多种分隔符
      let values: string[] = []
      if (line.includes('\t')) {
        values = line.split('\t')
      }
      else if (line.includes(',')) {
        values = line.split(',')
      }
      else if (line.includes(';')) {
        values = line.split(';')
      }
      else {
        values = [line] // 单个值
      }

      // 创建新行数据
      const row: ChartDatasetCell[][] = []
      fieldsJson.value.forEach((field, fieldIndex) => {
        let value: any = values[fieldIndex]?.trim() || ''

        // 根据字段类型转换值
        if (field.type === 1 && value) {
          // 数字类型
          const numValue = Number(value)
          if (!Number.isNaN(numValue)) {
            value = numValue
          }
        }
        else if (field.type === 3 && value) {
          // 布尔类型
          value = ['true', '1', '是', 'yes'].includes(value.toLowerCase())
        }

        // 创建 ChartDatasetCell
        const cell = new ChartDatasetCell()
        cell.value = value
        cell.updatedAt = dayjs()
        row.push([cell])
      })

      newRecords.push(row)
    })

    if (newRecords.length > 0) {
      records.value.push(...newRecords)
      message.success(`成功录入 ${newRecords.length} 条数据`)
      showBatchInputModal.value = false
      batchInputText.value = ''
    }
    else {
      message.warning('没有有效的数据行')
    }
  }
  catch (error) {
    console.error('批量录入错误:', error)
    message.error('数据格式错误，请检查输入格式')
  }
}

// 编辑历史记录相关状态
const showEditHistoryModal = ref(false)
const editingHistory = ref<ChartDatasetCell>({} as ChartDatasetCell)
const editingHistoryIndex = ref(-1)

// 编辑历史记录
function editHistoryRecord(record: ChartDatasetCell, index: number) {
  editingHistory.value = { ...record }
  editingHistoryIndex.value = index
  showEditHistoryModal.value = true
}

// 删除历史记录
function deleteHistoryRecord(record: ChartDatasetCell, index: number) {
  const history = indicatorDetailData.value.history
  if (history.length === 1) {
    record.value = EMPTY_PLACEHOLDER
    switchToHistoryVersion(record)
    showIndicatorDetailModal.value = false
  }
  else {
  // 从历史记录中删除
    history.splice(index, 1)

    // 如果删除的是当前显示的记录，切换到最新记录
    if (index === indicatorDetailData.value.index) {
      switchToHistoryVersion(history[0]!)
    }
    else if (index < indicatorDetailData.value.index) {
    // 如果删除的记录在当前记录之前，需要调整索引
      indicatorDetailData.value.index--
    }
  }

  message.success('历史记录删除成功')
}

// 保存历史记录编辑
function saveHistoryRecord() {
  const history = indicatorDetailData.value.history
  const index = editingHistoryIndex.value

  if (index >= 0 && index < history.length) {
    // 更新历史记录
    Object.assign(history[index]!, editingHistory.value)

    // 如果编辑的是当前显示的记录，更新显示
    if (index === indicatorDetailData.value.index) {
      switchToHistoryVersion(history[index]!)
    }

    message.success('历史记录更新成功')
    showEditHistoryModal.value = false
  }
}

// 取消编辑历史记录
function cancelEditHistory() {
  showEditHistoryModal.value = false
  editingHistory.value = {} as ChartDatasetCell
  editingHistoryIndex.value = -1
}

// 根据字段类型获取列宽
function getColumnWidth(fieldType: number): number {
  switch (fieldType) {
    case ChartFieldType.Number: return 120 // 数字字段
    case ChartFieldType.Date: return 140 // 日期字段
    case ChartFieldType.Boolean: return 80 // 布尔字段
    default: return 150 // 字符串字段
  }
}

function getCellComponent(column: any) {
  const fieldIndex = Number(column.dataIndex)
  const field = fieldsJson.value[fieldIndex]

  if (field && field.name === '国家') {
    return h(StandardSelect, { sKey: 'guojia', dropdownMatchSelectWidth: false })
  }
  switch (column.type) {
    case ChartFieldType.Number: return InputNumber
    case ChartFieldType.Date: return DatePicker
    case ChartFieldType.Boolean: return CSwitch
    default: return Input
  }
}
function cancelEdit() {
  Modal.confirm({
    content: '不会保存您的操作，确定要关闭吗？',
    onOk() {
      editVisible.value = false
    },
  })
}

async function handleEditOk(close = false) {
  try {
    if (!editForm.value.name) {
      message.warning('请填写数据集名称')
      return
    }

    let data: ChartDatasetView
    // 组装fieldsJson和records
    editForm.value.fieldsJson = fieldsJson.value
    // records现在是二维数组，直接赋值
    editForm.value.records = records.value
    if (editForm.value.id) {
      data = await Api.ChartStatistices.UpdateDataset_PostAsync(
        { id: String(editForm.value.id) },
        editForm.value as ChartDatasetView,
      )

      message.success('修改成功')
    }
    else {
      data = await Api.ChartStatistices.CreateDataset_PostAsync(editForm.value as ChartDatasetView)
      message.success('新增成功')
    }

    editForm.value = { ...data }
    fieldsJson.value = Array.isArray(data.fieldsJson) ? JSON.parse(JSON.stringify(data.fieldsJson)) : []
    records.value = data.records ?? []

    isSaved.value = true
    editVisible.value = !close
    if (editVisible.value === false)
      tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`操作失败: ${e.message || e}`)
  }
}
async function deleteDataset(record: ChartDatasetView) {
  try {
    await Api.ChartStatistices.DeleteDataset_PostAsync({ id: String(record.id) })
    message.success('删除成功')
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`删除失败: ${e.message || e}`)
  }
}

// 拖拽相关功能
let fieldsSortable: typeof Sortable | null = null

// 初始化字段表格拖拽功能
function initFieldsSortable() {
  if (!fieldsTableRef.value)
    return

  nextTick(() => {
    const tbody = fieldsTableRef.value.$el.querySelector('.ant-table-tbody')
    if (!tbody)
      return

    if (fieldsSortable) {
      fieldsSortable.destroy()
    }

    fieldsSortable = new Sortable(tbody, {
      animation: 150,
      handle: '.i-mdi-drag-vertical', // 只能通过拖拽图标拖拽
      onEnd: (evt: any) => {
        const { oldIndex, newIndex } = evt

        if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
          // 暂时禁用响应式，避免触发watch
          const tempFields = [...fieldsJson.value]

          // 移动字段
          const movedField = tempFields.splice(oldIndex, 1)[0]
          tempFields.splice(newIndex, 0, movedField!)

          // 一次性更新字段数组
          fieldsJson.value = tempFields

          // 同步数据行的字段顺序
          syncRecordsWithFieldOrder(oldIndex, newIndex)
        }
      },
    })
  })
}

// 同步数据行的字段顺序
function syncRecordsWithFieldOrder(oldIndex: number, newIndex: number) {
  records.value = records.value.map((row: ChartDatasetCell[][]) => {
    const newRow = [...row]
    // 移动对应位置的数据
    const movedData = newRow.splice(oldIndex, 1)[0]
    newRow.splice(newIndex, 0, movedData!)
    return newRow
  })
}

onMounted(async () => {
  predefinedCountries.value = (await byKeyApi({ key: 'guojia' })).standardItems?.map(item => item.label) || []
})
</script>

<style scoped lang="less">
:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}

// 拖拽样式
:deep(.sortable-ghost) {
  opacity: 0.4;
  background: #f0f0f0;
}

:deep(.sortable-chosen) {
  background: #e6f7ff;
}

:deep(.sortable-drag) {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

// 拖拽图标样式
.i-mdi-drag-vertical {
  transition: color 0.2s ease;

  &:hover {
    color: #1890ff !important;
  }
}

// 顺序调整按钮样式
:deep(.ant-btn-text) {
  padding: 0 4px;
  height: 20px;
  line-height: 20px;

  &:hover {
    background: #f0f0f0;
  }

  &:disabled {
    color: #d9d9d9;
  }
}

// 批量操作工具栏样式
.batch-toolbar {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

// 指标数据详情模态框样式
.indicator-detail-content {
  .detail-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;
    }
  }

  .current-version-card {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 8px;
    padding: 16px;
  }

  .history-timeline {
    position: relative;

    .timeline-item {
      position: relative;
      padding-left: 24px;
      margin-bottom: 16px;

      &:not(:last-child)::before {
        content: '';
        position: absolute;
        left: 7px;
        top: 20px;
        bottom: -16px;
        width: 2px;
        background: #e8e8e8;
      }

      .timeline-marker {
        position: absolute;
        left: 0;
        top: 8px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: #d9d9d9;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #e8e8e8;
      }

      &.is-current .timeline-marker {
        background: #52c41a;
        box-shadow: 0 0 0 2px #b7eb8f;
      }

      .timeline-content {
        background: #fafafa;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 12px;

        &:hover {
          border-color: #d9d9d9;
        }
      }

      &.is-current .timeline-content {
        background: #f6ffed;
        border-color: #b7eb8f;
      }
    }
  }
}
</style>
